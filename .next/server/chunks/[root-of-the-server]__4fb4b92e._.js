module.exports = {

"[project]/.next-internal/server/app/api/orders/create/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "OrderService": ()=>OrderService,
    "PaymentService": ()=>PaymentService,
    "prisma": ()=>prisma
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = globalThis.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) {
    globalThis.prisma = prisma;
}
class OrderService {
    // 创建订单
    static async createOrder(userEmail, amount) {
        const order = await prisma.order.create({
            data: {
                userEmail,
                amount,
                status: 'PENDING'
            }
        });
        return {
            id: order.id,
            userEmail: order.userEmail,
            amount: order.amount,
            status: order.status,
            augmentEmail: order.augmentEmail || undefined,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt
        };
    }
    // 根据ID获取订单
    static async getOrderById(orderId) {
        const order = await prisma.order.findUnique({
            where: {
                id: orderId
            },
            include: {
                payments: true
            }
        });
        if (!order) return null;
        return {
            id: order.id,
            userEmail: order.userEmail,
            amount: order.amount,
            status: order.status,
            augmentEmail: order.augmentEmail || undefined,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt
        };
    }
    // 更新订单状态
    static async updateOrderStatus(orderId, status) {
        const order = await prisma.order.update({
            where: {
                id: orderId
            },
            data: {
                status,
                updatedAt: new Date()
            }
        });
        return {
            id: order.id,
            userEmail: order.userEmail,
            amount: order.amount,
            status: order.status,
            augmentEmail: order.augmentEmail || undefined,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt
        };
    }
    // 分配 Augment 账号给订单
    static async assignAugmentAccount(orderId, augmentEmail) {
        const order = await prisma.order.update({
            where: {
                id: orderId
            },
            data: {
                augmentEmail,
                status: 'COMPLETED',
                updatedAt: new Date()
            }
        });
        return {
            id: order.id,
            userEmail: order.userEmail,
            amount: order.amount,
            status: order.status,
            augmentEmail: order.augmentEmail || undefined,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt
        };
    }
}
class PaymentService {
    // 创建支付记录
    static async createPayment(orderId, amount, qrCodeUrl) {
        const payment = await prisma.payment.create({
            data: {
                orderId,
                amount,
                status: 'PENDING',
                qrCodeUrl
            }
        });
        return {
            id: payment.id,
            orderId: payment.orderId,
            amount: payment.amount,
            status: payment.status,
            wechatOrderId: payment.wechatOrderId || undefined,
            qrCodeUrl: payment.qrCodeUrl || undefined,
            createdAt: payment.createdAt,
            updatedAt: payment.updatedAt
        };
    }
    // 更新支付状态
    static async updatePaymentStatus(paymentId, status, wechatOrderId) {
        const payment = await prisma.payment.update({
            where: {
                id: paymentId
            },
            data: {
                status,
                wechatOrderId,
                updatedAt: new Date()
            }
        });
        return {
            id: payment.id,
            orderId: payment.orderId,
            amount: payment.amount,
            status: payment.status,
            wechatOrderId: payment.wechatOrderId || undefined,
            qrCodeUrl: payment.qrCodeUrl || undefined,
            createdAt: payment.createdAt,
            updatedAt: payment.updatedAt
        };
    }
    // 根据订单ID获取支付记录
    static async getPaymentByOrderId(orderId) {
        const payment = await prisma.payment.findFirst({
            where: {
                orderId
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        if (!payment) return null;
        return {
            id: payment.id,
            orderId: payment.orderId,
            amount: payment.amount,
            status: payment.status,
            wechatOrderId: payment.wechatOrderId || undefined,
            qrCodeUrl: payment.qrCodeUrl || undefined,
            createdAt: payment.createdAt,
            updatedAt: payment.updatedAt
        };
    }
}
}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "amountToFen": ()=>amountToFen,
    "delay": ()=>delay,
    "formatAmount": ()=>formatAmount,
    "generateNonceStr": ()=>generateNonceStr,
    "generateOrderId": ()=>generateOrderId,
    "generatePaymentId": ()=>generatePaymentId,
    "generateWechatSignature": ()=>generateWechatSignature,
    "isValidEmail": ()=>isValidEmail,
    "verifyWechatSignature": ()=>verifyWechatSignature,
    "withErrorHandling": ()=>withErrorHandling
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
function generateOrderId() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `ORDER_${timestamp}_${random}`;
}
function generatePaymentId() {
    return `PAY_${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])().replace(/-/g, '').substring(0, 16)}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function formatAmount(amount) {
    return (amount / 100).toFixed(2);
}
function amountToFen(amount) {
    return Math.round(amount * 100);
}
function generateWechatSignature(params, apiKey) {
    // 按字典序排序参数
    const sortedKeys = Object.keys(params).sort();
    const stringA = sortedKeys.map((key)=>`${key}=${params[key]}`).join('&');
    const stringSignTemp = `${stringA}&key=${apiKey}`;
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
}
function verifyWechatSignature(params, signature, apiKey) {
    const { sign, ...otherParams } = params;
    const calculatedSignature = generateWechatSignature(otherParams, apiKey);
    return calculatedSignature === signature;
}
function generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for(let i = 0; i < length; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
function delay(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function withErrorHandling(fn) {
    return async (...args)=>{
        try {
            const data = await fn(...args);
            return {
                success: true,
                data
            };
        } catch (error) {
            console.error('Error in function:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    };
}
}),
"[project]/src/lib/wechat-pay.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createWechatPayOrder": ()=>createWechatPayOrder,
    "generateV3WechatNotificationResponse": ()=>generateV3WechatNotificationResponse,
    "parseV3WechatNotification": ()=>parseV3WechatNotification,
    "queryWechatPayOrder": ()=>queryWechatPayOrder,
    "verifyV3CallbackSignature": ()=>verifyV3CallbackSignature
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
;
;
;
// 微信支付 API v3 配置
const WECHAT_CONFIG = {
    appId: process.env.WECHAT_APP_ID,
    mchId: process.env.WECHAT_MCH_ID,
    apiV3Key: process.env.WECHAT_API_V3_KEY,
    certPath: process.env.WECHAT_CERT_PATH,
    keyPath: process.env.WECHAT_KEY_PATH,
    certSerialNumber: process.env.WECHAT_CERT_SERIAL_NUMBER,
    notifyUrl: `${("TURBOPACK compile-time value", "http://localhost:3000")}/api/payment/notify`,
    baseUrl: 'https://api.mch.weixin.qq.com'
};
// 读取私钥文件
function getPrivateKey() {
    try {
        const keyPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(process.cwd(), WECHAT_CONFIG.keyPath);
        return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(keyPath, 'utf8');
    } catch (error) {
        console.error('读取私钥文件失败:', error);
        throw new Error('私钥文件不存在或无法读取，请确保证书文件已正确配置');
    }
}
// 生成微信支付 API v3 签名
function generateV3Signature(method, url, timestamp, nonce, body) {
    // 构造签名串
    const message = `${method}\n${url}\n${timestamp}\n${nonce}\n${body}\n`;
    try {
        const privateKey = getPrivateKey();
        // 使用 SHA256withRSA 签名
        const sign = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createSign('RSA-SHA256');
        sign.update(message, 'utf8');
        const signature = sign.sign(privateKey, 'base64');
        console.log('=== 微信支付 API v3 签名调试 ===');
        console.log('1. 签名方法:', method);
        console.log('2. 请求URL:', url);
        console.log('3. 时间戳:', timestamp);
        console.log('4. 随机字符串:', nonce);
        console.log('5. 请求体:', body);
        console.log('6. 签名消息:', message);
        console.log('7. 生成签名:', signature);
        console.log('================================');
        return signature;
    } catch (error) {
        console.error('生成签名失败:', error);
        throw new Error('签名生成失败，请检查私钥文件');
    }
}
// 生成 Authorization 头
function generateAuthorizationHeader(method, url, body) {
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateNonceStr"])();
    const signature = generateV3Signature(method, url, timestamp, nonce, body);
    const serialNumber = WECHAT_CONFIG.certSerialNumber || 'CERT_SERIAL_NUMBER_PLACEHOLDER';
    return `WECHATPAY2-SHA256-RSA2048 mchid="${WECHAT_CONFIG.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${serialNumber}"`;
}
function verifyV3CallbackSignature(timestamp, nonce, body, signature, serialNumber) {
    try {
        // 构造验签名串
        const message = `${timestamp}\n${nonce}\n${body}\n`;
        // 这里应该使用微信支付平台证书来验证签名
        // 为了简化，我们先返回 true，实际生产环境需要实现完整的证书验证
        console.log('验证回调签名:', {
            timestamp,
            nonce,
            body: body.substring(0, 100),
            signature,
            serialNumber
        });
        return true; // 临时返回 true，实际需要用平台证书验证
    } catch (error) {
        console.error('验证回调签名失败:', error);
        return false;
    }
}
// 解密微信支付 API v3 回调数据
function decryptV3CallbackData(ciphertext, associatedData, nonce) {
    try {
        const key = WECHAT_CONFIG.apiV3Key;
        // 使用 AES-256-GCM 解密
        const decipher = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createDecipherGCM('aes-256-gcm', Buffer.from(key, 'utf8'));
        decipher.setAAD(Buffer.from(associatedData, 'utf8'));
        const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
        const tag = ciphertextBuffer.slice(-16);
        const encrypted = ciphertextBuffer.slice(0, -16);
        decipher.setAuthTag(tag);
        let decrypted = decipher.update(encrypted, undefined, 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    } catch (error) {
        console.error('解密回调数据失败:', error);
        throw new Error('解密回调数据失败');
    }
}
async function createWechatPayOrder(orderId, amount, description, clientIp = '127.0.0.1') {
    try {
        // 构造请求参数
        const requestData = {
            appid: WECHAT_CONFIG.appId,
            mchid: WECHAT_CONFIG.mchId,
            description: description,
            out_trade_no: orderId,
            notify_url: WECHAT_CONFIG.notifyUrl,
            amount: {
                total: amount,
                currency: 'CNY'
            },
            scene_info: {
                payer_client_ip: clientIp
            }
        };
        const requestBody = JSON.stringify(requestData);
        const url = '/v3/pay/transactions/native';
        const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;
        // 生成 Authorization 头
        const authorization = generateAuthorizationHeader('POST', url, requestBody);
        console.log('=== 微信支付 API v3 请求调试 ===');
        console.log('1. 请求URL:', fullUrl);
        console.log('2. 请求体:', requestBody);
        console.log('3. Authorization:', authorization);
        console.log('================================');
        // 发送请求
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(fullUrl, requestBody, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': authorization,
                'User-Agent': 'Augment-Pay/1.0'
            },
            timeout: 10000
        });
        console.log('微信支付 API v3 响应:', response.data);
        const responseData = response.data;
        return {
            success: true,
            qrCodeUrl: responseData.code_url
        };
    } catch (error) {
        console.error('创建微信支付订单失败:', error);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
            const errorMessage = error.response?.data?.message || error.message;
            console.error('API 错误详情:', error.response?.data);
            return {
                success: false,
                error: `微信支付API错误: ${errorMessage}`
            };
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : '创建支付订单失败'
        };
    }
}
async function queryWechatPayOrder(orderId) {
    try {
        const url = `/v3/pay/transactions/out-trade-no/${orderId}?mchid=${WECHAT_CONFIG.mchId}`;
        const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;
        // 生成 Authorization 头（GET 请求，body 为空字符串）
        const authorization = generateAuthorizationHeader('GET', url, '');
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(fullUrl, {
            headers: {
                'Accept': 'application/json',
                'Authorization': authorization,
                'User-Agent': 'Augment-Pay/1.0'
            },
            timeout: 10000
        });
        const responseData = response.data;
        console.log('查询订单响应:', responseData);
        // 检查支付状态
        const isPaid = responseData.trade_state === 'SUCCESS';
        return {
            success: true,
            isPaid
        };
    } catch (error) {
        console.error('查询微信支付订单状态失败:', error);
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
            const errorMessage = error.response?.data?.message || error.message;
            console.error('查询API错误详情:', error.response?.data);
            return {
                success: false,
                error: `查询订单失败: ${errorMessage}`
            };
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : '查询支付状态失败'
        };
    }
}
function parseV3WechatNotification(requestBody, signature, timestamp, nonce, serialNumber) {
    try {
        // 验证签名
        if (!verifyV3CallbackSignature(timestamp, nonce, requestBody, signature, serialNumber)) {
            return {
                success: false,
                error: '签名验证失败'
            };
        }
        const notifyData = JSON.parse(requestBody);
        // 解密回调数据
        const decryptedData = decryptV3CallbackData(notifyData.resource.ciphertext, notifyData.resource.associated_data, notifyData.resource.nonce);
        const paymentData = JSON.parse(decryptedData);
        return {
            success: true,
            data: paymentData
        };
    } catch (error) {
        console.error('解析微信支付 API v3 回调失败:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '解析回调数据失败'
        };
    }
}
function generateV3WechatNotificationResponse(success, message) {
    if (success) {
        return {
            code: 'SUCCESS',
            message: message || 'OK'
        };
    } else {
        return {
            code: 'FAIL',
            message: message || 'FAIL'
        };
    }
}
}),
"[project]/src/app/api/orders/create/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$wechat$2d$pay$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/wechat-pay.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { userEmail } = body;
        // 验证请求参数
        if (!userEmail) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '邮箱地址不能为空'
            }, {
                status: 400
            });
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidEmail"])(userEmail)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '邮箱地址格式不正确'
            }, {
                status: 400
            });
        }
        // 获取价格配置
        const price = parseFloat(process.env.AUGMENT_ACCOUNT_PRICE || '99');
        const amountInFen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["amountToFen"])(price);
        // 创建订单
        const order = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OrderService"].createOrder(userEmail, amountInFen);
        // 获取客户端IP
        const clientIp = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1';
        // 创建微信支付订单
        const wechatPayResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$wechat$2d$pay$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createWechatPayOrder"])(order.id, amountInFen, 'Augment 账号购买', clientIp);
        if (!wechatPayResult.success) {
            // 如果微信支付创建失败，更新订单状态为失败
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OrderService"].updateOrderStatus(order.id, 'FAILED');
            console.error('微信支付创建失败:', wechatPayResult.error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: wechatPayResult.error || '创建支付订单失败'
            }, {
                status: 500
            });
        }
        // 创建支付记录
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PaymentService"].createPayment(order.id, amountInFen, wechatPayResult.qrCodeUrl);
        // 返回成功响应
        const response = {
            orderId: order.id,
            qrCodeUrl: wechatPayResult.qrCodeUrl,
            amount: price
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: response
        });
    } catch (error) {
        console.error('创建订单失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '服务器内部错误'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4fb4b92e._.js.map