{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/lib/utils.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport crypto from 'crypto';\n\n// 生成唯一订单号\nexport function generateOrderId(): string {\n  const timestamp = Date.now().toString();\n  const random = Math.random().toString(36).substring(2, 8);\n  return `ORDER_${timestamp}_${random}`;\n}\n\n// 生成唯一支付ID\nexport function generatePaymentId(): string {\n  return `PAY_${uuidv4().replace(/-/g, '').substring(0, 16)}`;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 格式化金额（分转元）\nexport function formatAmount(amount: number): string {\n  return (amount / 100).toFixed(2);\n}\n\n// 金额转换（元转分）\nexport function amountToFen(amount: number): number {\n  return Math.round(amount * 100);\n}\n\n// 生成微信支付签名\nexport function generateWechatSignature(\n  params: Record<string, any>,\n  apiKey: string\n): string {\n  // 按字典序排序参数\n  const sortedKeys = Object.keys(params).sort();\n  const stringA = sortedKeys\n    .map(key => `${key}=${params[key]}`)\n    .join('&');\n  \n  const stringSignTemp = `${stringA}&key=${apiKey}`;\n  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();\n}\n\n// 验证微信支付回调签名\nexport function verifyWechatSignature(\n  params: Record<string, any>,\n  signature: string,\n  apiKey: string\n): boolean {\n  const { sign, ...otherParams } = params;\n  const calculatedSignature = generateWechatSignature(otherParams, apiKey);\n  return calculatedSignature === signature;\n}\n\n// 生成随机字符串\nexport function generateNonceStr(length: number = 32): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 延迟函数\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// 错误处理包装器\nexport function withErrorHandling<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<{ success: boolean; data?: R; error?: string }> => {\n    try {\n      const data = await fn(...args);\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error in function:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;IACrC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,QAAQ;AACvC;AAGO,SAAS;IACd,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK;AAC7D;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,MAAc;IACzC,OAAO,CAAC,SAAS,GAAG,EAAE,OAAO,CAAC;AAChC;AAGO,SAAS,YAAY,MAAc;IACxC,OAAO,KAAK,KAAK,CAAC,SAAS;AAC7B;AAGO,SAAS,wBACd,MAA2B,EAC3B,MAAc;IAEd,WAAW;IACX,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,IAAI;IAC3C,MAAM,UAAU,WACb,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,EAClC,IAAI,CAAC;IAER,MAAM,iBAAiB,GAAG,QAAQ,KAAK,EAAE,QAAQ;IACjD,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,gBAAgB,MAAM,CAAC,OAAO,WAAW;AAClF;AAGO,SAAS,sBACd,MAA2B,EAC3B,SAAiB,EACjB,MAAc;IAEd,MAAM,EAAE,IAAI,EAAE,GAAG,aAAa,GAAG;IACjC,MAAM,sBAAsB,wBAAwB,aAAa;IACjE,OAAO,wBAAwB;AACjC;AAGO,SAAS,iBAAiB,SAAiB,EAAE;IAClD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,kBACd,EAA8B;IAE9B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,MAAM,OAAO,MAAM,MAAM;YACzB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/components/PaymentModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport QRCode from 'qrcode';\n\ninterface PaymentModalProps {\n  orderId: string;\n  qrCodeUrl: string;\n  amount: number;\n  onSuccess: (augmentEmail: string) => void;\n  onClose: () => void;\n}\n\nexport default function PaymentModal({\n  orderId,\n  qrCodeUrl,\n  amount,\n  onSuccess,\n  onClose\n}: PaymentModalProps) {\n  const [qrCodeDataUrl, setQrCodeDataUrl] = useState('');\n  const [isPolling, setIsPolling] = useState(true);\n  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15分钟倒计时\n\n  // 生成二维码\n  useEffect(() => {\n    const generateQRCode = async () => {\n      try {\n        const dataUrl = await QRCode.toDataURL(qrCodeUrl, {\n          width: 256,\n          margin: 2,\n          color: {\n            dark: '#000000',\n            light: '#FFFFFF'\n          }\n        });\n        setQrCodeDataUrl(dataUrl);\n      } catch (error) {\n        console.error('生成二维码失败:', error);\n      }\n    };\n\n    if (qrCodeUrl) {\n      generateQRCode();\n    }\n  }, [qrCodeUrl]);\n\n  // 轮询支付状态\n  useEffect(() => {\n    if (!isPolling) return;\n\n    const pollPaymentStatus = async () => {\n      try {\n        const response = await fetch(`/api/orders/status?orderId=${orderId}`);\n        const data = await response.json();\n\n        if (data.success && data.data.status === 'COMPLETED' && data.data.augmentEmail) {\n          setIsPolling(false);\n          onSuccess(data.data.augmentEmail);\n        }\n      } catch (error) {\n        console.error('查询支付状态失败:', error);\n      }\n    };\n\n    const interval = setInterval(pollPaymentStatus, 3000); // 每3秒查询一次\n\n    return () => clearInterval(interval);\n  }, [orderId, isPolling, onSuccess]);\n\n  // 倒计时\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      setIsPolling(false);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      setTimeLeft(timeLeft - 1);\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, [timeLeft]);\n\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleClose = () => {\n    setIsPolling(false);\n    onClose();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-md w-full p-6 relative\">\n        {/* 关闭按钮 */}\n        <button\n          onClick={handleClose}\n          className=\"absolute top-4 right-4 text-gray-400 hover:text-gray-600\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        </button>\n\n        {/* 标题 */}\n        <div className=\"text-center mb-6\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-2\">微信支付</h3>\n          <p className=\"text-gray-600\">请使用微信扫描下方二维码完成支付</p>\n        </div>\n\n        {/* 订单信息 */}\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <span className=\"text-gray-600\">商品</span>\n            <span className=\"font-medium\">Augment 账号</span>\n          </div>\n          <div className=\"flex justify-between items-center mb-2\">\n            <span className=\"text-gray-600\">订单号</span>\n            <span className=\"font-mono text-sm\">{orderId}</span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">金额</span>\n            <span className=\"font-bold text-lg text-blue-600\">¥{amount}</span>\n          </div>\n        </div>\n\n        {/* 二维码 */}\n        <div className=\"text-center mb-6\">\n          {qrCodeDataUrl ? (\n            <div className=\"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg\">\n              <img\n                src={qrCodeDataUrl}\n                alt=\"支付二维码\"\n                className=\"w-48 h-48 mx-auto\"\n              />\n            </div>\n          ) : (\n            <div className=\"w-48 h-48 mx-auto bg-gray-200 rounded-lg flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          )}\n        </div>\n\n        {/* 倒计时和状态 */}\n        <div className=\"text-center mb-4\">\n          {timeLeft > 0 ? (\n            <div className=\"flex items-center justify-center space-x-2\">\n              <svg className=\"w-5 h-5 text-orange-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clipRule=\"evenodd\" />\n              </svg>\n              <span className=\"text-orange-600 font-medium\">\n                支付剩余时间: {formatTime(timeLeft)}\n              </span>\n            </div>\n          ) : (\n            <div className=\"text-red-600 font-medium\">\n              支付已超时，请重新创建订单\n            </div>\n          )}\n        </div>\n\n        {/* 支付状态 */}\n        {isPolling && timeLeft > 0 && (\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center space-x-2 text-blue-600\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n              <span>等待支付中...</span>\n            </div>\n          </div>\n        )}\n\n        {/* 支付说明 */}\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-500\">\n            支付成功后，系统将自动为您分配 Augment 账号\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,aAAa,EACnC,OAAO,EACP,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACW;IAClB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,UAAU;IAE7D,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,UAAU,MAAM,sIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,WAAW;oBAChD,OAAO;oBACP,QAAQ;oBACR,OAAO;wBACL,MAAM;wBACN,OAAO;oBACT;gBACF;gBACA,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,YAAY;YAC5B;QACF;QAEA,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAU;IAEd,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,SAAS;gBACpE,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,IAAI,CAAC,YAAY,EAAE;oBAC9E,aAAa;oBACb,UAAU,KAAK,IAAI,CAAC,YAAY;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA,MAAM,WAAW,YAAY,mBAAmB,OAAO,UAAU;QAEjE,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAS;QAAW;KAAU;IAElC,MAAM;IACN,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,GAAG;YACjB,aAAa;YACb;QACF;QAEA,MAAM,QAAQ,WAAW;YACvB,YAAY,WAAW;QACzB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACjG;IAEA,MAAM,cAAc;QAClB,aAAa;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAKzE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;sCAEhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;;wCAAkC;wCAAE;;;;;;;;;;;;;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;8BACZ,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;;;;;6CAId,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;8BACZ,WAAW,kBACV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0B,MAAK;gCAAe,SAAQ;0CACnE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqH,UAAS;;;;;;;;;;;0CAE3J,8OAAC;gCAAK,WAAU;;oCAA8B;oCACnC,WAAW;;;;;;;;;;;;6CAIxB,8OAAC;wBAAI,WAAU;kCAA2B;;;;;;;;;;;gBAO7C,aAAa,WAAW,mBACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/components/PurchaseForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { isValidEmail } from '@/lib/utils';\nimport PaymentModal from './PaymentModal';\n\nexport default function PurchaseForm() {\n  const [email, setEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showPayment, setShowPayment] = useState(false);\n  const [orderId, setOrderId] = useState('');\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [error, setError] = useState('');\n  const [price, setPrice] = useState(99);\n  const [priceDisplay, setPriceDisplay] = useState('¥99');\n\n  useEffect(() => {\n    // 获取价格配置\n    const fetchPrice = async () => {\n      try {\n        const response = await fetch('/api/config/price');\n        const data = await response.json();\n        if (data.success) {\n          setPrice(data.data.price);\n          setPriceDisplay(data.data.priceDisplay);\n        }\n      } catch (error) {\n        console.error('获取价格配置失败:', error);\n      }\n    };\n\n    fetchPrice();\n  }, []);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    // 验证邮箱\n    if (!email.trim()) {\n      setError('请输入邮箱地址');\n      return;\n    }\n\n    if (!isValidEmail(email)) {\n      setError('请输入有效的邮箱地址');\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // 创建订单\n      const response = await fetch('/api/orders/create', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ userEmail: email }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || '创建订单失败');\n      }\n\n      // 显示支付模态框\n      setOrderId(data.data.orderId);\n      setQrCodeUrl(data.data.qrCodeUrl);\n      setShowPayment(true);\n    } catch (error) {\n      console.error('创建订单失败:', error);\n      setError(error instanceof Error ? error.message : '创建订单失败，请重试');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handlePaymentSuccess = (augmentEmail: string) => {\n    setShowPayment(false);\n    // 这里可以显示成功页面或重定向\n    alert(`支付成功！您的 Augment 账号：${augmentEmail}`);\n    setEmail('');\n    setOrderId('');\n    setQrCodeUrl('');\n  };\n\n  const handlePaymentClose = () => {\n    setShowPayment(false);\n    setOrderId('');\n    setQrCodeUrl('');\n  };\n\n  return (\n    <>\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">\n          立即购买 Augment 账号\n        </h2>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              您的邮箱地址\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder=\"请输入您的邮箱地址\"\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n              disabled={isLoading}\n            />\n            <p className=\"text-sm text-gray-500 mt-2\">\n              我们将向此邮箱发送 Augment 账号信息\n            </p>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <svg className=\"w-5 h-5 text-red-400 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"text-red-700 text-sm\">{error}</span>\n              </div>\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center\"\n          >\n            {isLoading ? (\n              <>\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                创建订单中...\n              </>\n            ) : (\n              `立即购买 ${priceDisplay}`\n            )}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-500\">\n            支持微信支付 • 安全可靠 • 即时交付\n          </p>\n        </div>\n      </div>\n\n      {/* 支付模态框 */}\n      {showPayment && (\n        <PaymentModal\n          orderId={orderId}\n          qrCodeUrl={qrCodeUrl}\n          amount={99}\n          onSuccess={handlePaymentSuccess}\n          onClose={handlePaymentClose}\n        />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;oBACxB,gBAAgB,KAAK,IAAI,CAAC,YAAY;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,OAAO;QACP,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;YACxB,SAAS;YACT;QACF;QAEA,aAAa;QAEb,IAAI;YACF,OAAO;YACP,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,WAAW;gBAAM;YAC1C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,UAAU;YACV,WAAW,KAAK,IAAI,CAAC,OAAO;YAC5B,aAAa,KAAK,IAAI,CAAC,SAAS;YAChC,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;QACf,iBAAiB;QACjB,MAAM,CAAC,mBAAmB,EAAE,cAAc;QAC1C,SAAS;QACT,WAAW;QACX,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,WAAW;QACX,aAAa;IACf;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAIlE,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;4BAK3C,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAA4B,MAAK;4CAAe,SAAQ;sDACrE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;sDAEhQ,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAK9C,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;mDAIR,CAAC,KAAK,EAAE,cAAc;;;;;;;;;;;;kCAK5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAOxC,6BACC,8OAAC,kIAAA,CAAA,UAAY;gBACX,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,WAAW;gBACX,SAAS;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport PurchaseForm from '@/components/PurchaseForm';\n\nexport default function Home() {\n  const [priceDisplay, setPriceDisplay] = useState('¥99');\n\n  useEffect(() => {\n    // 获取价格配置\n    const fetchPrice = async () => {\n      try {\n        const response = await fetch('/api/config/price');\n        const data = await response.json();\n        if (data.success) {\n          setPriceDisplay(data.data.priceDisplay);\n        }\n      } catch (error) {\n        console.error('获取价格配置失败:', error);\n      }\n    };\n\n    fetchPrice();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* 头部 */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Augment 账号\n            <span className=\"text-blue-600\">自动售卖</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            快速获取 Augment 账号，提升您的开发效率。\n            填写邮箱，微信支付，即刻获得专属账号。\n          </p>\n        </div>\n\n        {/* 特性介绍 */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-16\">\n          <div className=\"bg-white rounded-lg p-6 shadow-lg\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">即时交付</h3>\n            <p className=\"text-gray-600\">支付成功后立即获得 Augment 账号，无需等待</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-6 shadow-lg\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">安全可靠</h3>\n            <p className=\"text-gray-600\">微信支付保障，交易安全有保证</p>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-6 shadow-lg\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n              <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">24/7 服务</h3>\n            <p className=\"text-gray-600\">全天候自动化服务，随时购买随时使用</p>\n          </div>\n        </div>\n\n        {/* 购买表单 */}\n        <div className=\"max-w-md mx-auto\">\n          <PurchaseForm />\n        </div>\n\n        {/* 价格说明 */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white rounded-lg p-8 shadow-lg max-w-lg mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">价格透明</h3>\n            <div className=\"text-4xl font-bold text-blue-600 mb-2\">{priceDisplay}</div>\n            <p className=\"text-gray-600\">一次性购买，永久使用</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAKjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;8BAIf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CAAyC;;;;;;0CACxD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}