{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\nimport { Order, Payment, OrderStatus, PaymentStatus } from '@/types';\n\n// 全局 Prisma 客户端实例\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\nexport const prisma = globalThis.prisma || new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') {\n  globalThis.prisma = prisma;\n}\n\n// 订单相关操作\nexport class OrderService {\n  // 创建订单\n  static async createOrder(userEmail: string, amount: number): Promise<Order> {\n    const order = await prisma.order.create({\n      data: {\n        userEmail,\n        amount,\n        status: 'PENDING'\n      }\n    });\n    \n    return {\n      id: order.id,\n      userEmail: order.userEmail,\n      amount: order.amount,\n      status: order.status as OrderStatus,\n      augmentEmail: order.augmentEmail || undefined,\n      createdAt: order.createdAt,\n      updatedAt: order.updatedAt\n    };\n  }\n\n  // 根据ID获取订单\n  static async getOrderById(orderId: string): Promise<Order | null> {\n    const order = await prisma.order.findUnique({\n      where: { id: orderId },\n      include: { payments: true }\n    });\n\n    if (!order) return null;\n\n    return {\n      id: order.id,\n      userEmail: order.userEmail,\n      amount: order.amount,\n      status: order.status as OrderStatus,\n      augmentEmail: order.augmentEmail || undefined,\n      createdAt: order.createdAt,\n      updatedAt: order.updatedAt\n    };\n  }\n\n  // 更新订单状态\n  static async updateOrderStatus(orderId: string, status: OrderStatus): Promise<Order | null> {\n    const order = await prisma.order.update({\n      where: { id: orderId },\n      data: { \n        status,\n        updatedAt: new Date()\n      }\n    });\n\n    return {\n      id: order.id,\n      userEmail: order.userEmail,\n      amount: order.amount,\n      status: order.status as OrderStatus,\n      augmentEmail: order.augmentEmail || undefined,\n      createdAt: order.createdAt,\n      updatedAt: order.updatedAt\n    };\n  }\n\n  // 分配 Augment 账号给订单\n  static async assignAugmentAccount(orderId: string, augmentEmail: string): Promise<Order | null> {\n    const order = await prisma.order.update({\n      where: { id: orderId },\n      data: { \n        augmentEmail,\n        status: 'COMPLETED',\n        updatedAt: new Date()\n      }\n    });\n\n    return {\n      id: order.id,\n      userEmail: order.userEmail,\n      amount: order.amount,\n      status: order.status as OrderStatus,\n      augmentEmail: order.augmentEmail || undefined,\n      createdAt: order.createdAt,\n      updatedAt: order.updatedAt\n    };\n  }\n}\n\n// 支付相关操作\nexport class PaymentService {\n  // 创建支付记录\n  static async createPayment(\n    orderId: string, \n    amount: number, \n    qrCodeUrl?: string\n  ): Promise<Payment> {\n    const payment = await prisma.payment.create({\n      data: {\n        orderId,\n        amount,\n        status: 'PENDING',\n        qrCodeUrl\n      }\n    });\n\n    return {\n      id: payment.id,\n      orderId: payment.orderId,\n      amount: payment.amount,\n      status: payment.status as PaymentStatus,\n      wechatOrderId: payment.wechatOrderId || undefined,\n      qrCodeUrl: payment.qrCodeUrl || undefined,\n      createdAt: payment.createdAt,\n      updatedAt: payment.updatedAt\n    };\n  }\n\n  // 更新支付状态\n  static async updatePaymentStatus(\n    paymentId: string, \n    status: PaymentStatus,\n    wechatOrderId?: string\n  ): Promise<Payment | null> {\n    const payment = await prisma.payment.update({\n      where: { id: paymentId },\n      data: { \n        status,\n        wechatOrderId,\n        updatedAt: new Date()\n      }\n    });\n\n    return {\n      id: payment.id,\n      orderId: payment.orderId,\n      amount: payment.amount,\n      status: payment.status as PaymentStatus,\n      wechatOrderId: payment.wechatOrderId || undefined,\n      qrCodeUrl: payment.qrCodeUrl || undefined,\n      createdAt: payment.createdAt,\n      updatedAt: payment.updatedAt\n    };\n  }\n\n  // 根据订单ID获取支付记录\n  static async getPaymentByOrderId(orderId: string): Promise<Payment | null> {\n    const payment = await prisma.payment.findFirst({\n      where: { orderId },\n      orderBy: { createdAt: 'desc' }\n    });\n\n    if (!payment) return null;\n\n    return {\n      id: payment.id,\n      orderId: payment.orderId,\n      amount: payment.amount,\n      status: payment.status as PaymentStatus,\n      wechatOrderId: payment.wechatOrderId || undefined,\n      qrCodeUrl: payment.qrCodeUrl || undefined,\n      createdAt: payment.createdAt,\n      updatedAt: payment.updatedAt\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAQO,MAAM,SAAS,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE3D,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB;AAGO,MAAM;IACX,OAAO;IACP,aAAa,YAAY,SAAiB,EAAE,MAAc,EAAkB;QAC1E,MAAM,QAAQ,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ;gBACA;gBACA,QAAQ;YACV;QACF;QAEA,OAAO;YACL,IAAI,MAAM,EAAE;YACZ,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,cAAc,MAAM,YAAY,IAAI;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;QAC5B;IACF;IAEA,WAAW;IACX,aAAa,aAAa,OAAe,EAAyB;QAChE,MAAM,QAAQ,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAQ;YACrB,SAAS;gBAAE,UAAU;YAAK;QAC5B;QAEA,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAO;YACL,IAAI,MAAM,EAAE;YACZ,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,cAAc,MAAM,YAAY,IAAI;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;QAC5B;IACF;IAEA,SAAS;IACT,aAAa,kBAAkB,OAAe,EAAE,MAAmB,EAAyB;QAC1F,MAAM,QAAQ,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,IAAI;YAAQ;YACrB,MAAM;gBACJ;gBACA,WAAW,IAAI;YACjB;QACF;QAEA,OAAO;YACL,IAAI,MAAM,EAAE;YACZ,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,cAAc,MAAM,YAAY,IAAI;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;QAC5B;IACF;IAEA,mBAAmB;IACnB,aAAa,qBAAqB,OAAe,EAAE,YAAoB,EAAyB;QAC9F,MAAM,QAAQ,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,IAAI;YAAQ;YACrB,MAAM;gBACJ;gBACA,QAAQ;gBACR,WAAW,IAAI;YACjB;QACF;QAEA,OAAO;YACL,IAAI,MAAM,EAAE;YACZ,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,cAAc,MAAM,YAAY,IAAI;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;QAC5B;IACF;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,cACX,OAAe,EACf,MAAc,EACd,SAAkB,EACA;QAClB,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA;gBACA,QAAQ;gBACR;YACF;QACF;QAEA,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,SAAS,QAAQ,OAAO;YACxB,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,eAAe,QAAQ,aAAa,IAAI;YACxC,WAAW,QAAQ,SAAS,IAAI;YAChC,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA,SAAS;IACT,aAAa,oBACX,SAAiB,EACjB,MAAqB,EACrB,aAAsB,EACG;QACzB,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI;YACjB;QACF;QAEA,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,SAAS,QAAQ,OAAO;YACxB,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,eAAe,QAAQ,aAAa,IAAI;YACxC,WAAW,QAAQ,SAAS,IAAI;YAChC,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA,eAAe;IACf,aAAa,oBAAoB,OAAe,EAA2B;QACzE,MAAM,UAAU,MAAM,OAAO,OAAO,CAAC,SAAS,CAAC;YAC7C,OAAO;gBAAE;YAAQ;YACjB,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,IAAI,CAAC,SAAS,OAAO;QAErB,OAAO;YACL,IAAI,QAAQ,EAAE;YACd,SAAS,QAAQ,OAAO;YACxB,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,eAAe,QAAQ,aAAa,IAAI;YACxC,WAAW,QAAQ,SAAS,IAAI;YAChC,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;AACF", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/lib/utils.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport crypto from 'crypto';\n\n// 生成唯一订单号\nexport function generateOrderId(): string {\n  const timestamp = Date.now().toString();\n  const random = Math.random().toString(36).substring(2, 8);\n  return `ORDER_${timestamp}_${random}`;\n}\n\n// 生成唯一支付ID\nexport function generatePaymentId(): string {\n  return `PAY_${uuidv4().replace(/-/g, '').substring(0, 16)}`;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 格式化金额（分转元）\nexport function formatAmount(amount: number): string {\n  return (amount / 100).toFixed(2);\n}\n\n// 金额转换（元转分）\nexport function amountToFen(amount: number): number {\n  return Math.round(amount * 100);\n}\n\n// 生成微信支付签名\nexport function generateWechatSignature(\n  params: Record<string, any>,\n  apiKey: string\n): string {\n  // 按字典序排序参数\n  const sortedKeys = Object.keys(params).sort();\n  const stringA = sortedKeys\n    .map(key => `${key}=${params[key]}`)\n    .join('&');\n  \n  const stringSignTemp = `${stringA}&key=${apiKey}`;\n  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();\n}\n\n// 验证微信支付回调签名\nexport function verifyWechatSignature(\n  params: Record<string, any>,\n  signature: string,\n  apiKey: string\n): boolean {\n  const { sign, ...otherParams } = params;\n  const calculatedSignature = generateWechatSignature(otherParams, apiKey);\n  return calculatedSignature === signature;\n}\n\n// 生成随机字符串\nexport function generateNonceStr(length: number = 32): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 延迟函数\nexport function delay(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// 错误处理包装器\nexport function withErrorHandling<T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<{ success: boolean; data?: R; error?: string }> => {\n    try {\n      const data = await fn(...args);\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error in function:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;IACrC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,QAAQ;AACvC;AAGO,SAAS;IACd,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK;AAC7D;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,MAAc;IACzC,OAAO,CAAC,SAAS,GAAG,EAAE,OAAO,CAAC;AAChC;AAGO,SAAS,YAAY,MAAc;IACxC,OAAO,KAAK,KAAK,CAAC,SAAS;AAC7B;AAGO,SAAS,wBACd,MAA2B,EAC3B,MAAc;IAEd,WAAW;IACX,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,IAAI;IAC3C,MAAM,UAAU,WACb,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,EAClC,IAAI,CAAC;IAER,MAAM,iBAAiB,GAAG,QAAQ,KAAK,EAAE,QAAQ;IACjD,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,gBAAgB,MAAM,CAAC,OAAO,WAAW;AAClF;AAGO,SAAS,sBACd,MAA2B,EAC3B,SAAiB,EACjB,MAAc;IAEd,MAAM,EAAE,IAAI,EAAE,GAAG,aAAa,GAAG;IACjC,MAAM,sBAAsB,wBAAwB,aAAa;IACjE,OAAO,wBAAwB;AACjC;AAGO,SAAS,iBAAiB,SAAiB,EAAE;IAClD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,kBACd,EAA8B;IAE9B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,MAAM,OAAO,MAAM,MAAM;YACzB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/lib/wechat-pay.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport axios from 'axios';\nimport fs from 'fs';\nimport path from 'path';\nimport { generateNonceStr } from './utils';\n\n// 微信支付 API v3 配置\nconst WECHAT_CONFIG = {\n  appId: process.env.WECHAT_APP_ID!,\n  mchId: process.env.WECHAT_MCH_ID!,\n  apiV3Key: process.env.WECHAT_API_V3_KEY!,\n  certPath: process.env.WECHAT_CERT_PATH!,\n  keyPath: process.env.WECHAT_KEY_PATH!,\n  certSerialNumber: process.env.WECHAT_CERT_SERIAL_NUMBER!,\n  notifyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payment/notify`,\n  baseUrl: 'https://api.mch.weixin.qq.com'\n};\n\n// 微信支付 API v3 Native 下单请求接口\ninterface WechatPayV3NativeRequest {\n  appid: string;\n  mchid: string;\n  description: string;\n  out_trade_no: string;\n  notify_url: string;\n  amount: {\n    total: number;\n    currency: string;\n  };\n  scene_info?: {\n    payer_client_ip: string;\n  };\n}\n\n// 微信支付 API v3 Native 下单响应接口\ninterface WechatPayV3NativeResponse {\n  code_url: string;\n}\n\n// 微信支付 API v3 查询订单响应接口\ninterface WechatPayV3QueryResponse {\n  appid: string;\n  mchid: string;\n  out_trade_no: string;\n  transaction_id?: string;\n  trade_type: string;\n  trade_state: string;\n  trade_state_desc: string;\n  bank_type?: string;\n  attach?: string;\n  success_time?: string;\n  payer: {\n    openid: string;\n  };\n  amount: {\n    total: number;\n    payer_total?: number;\n    currency: string;\n    payer_currency?: string;\n  };\n}\n\n// 微信支付 API v3 回调通知接口\ninterface WechatPayV3NotifyRequest {\n  id: string;\n  create_time: string;\n  event_type: string;\n  resource_type: string;\n  resource: {\n    original_type: string;\n    algorithm: string;\n    ciphertext: string;\n    associated_data: string;\n    nonce: string;\n  };\n  summary: string;\n}\n\n// 读取私钥文件\nfunction getPrivateKey(): string {\n  try {\n    const keyPath = path.resolve(process.cwd(), WECHAT_CONFIG.keyPath);\n    return fs.readFileSync(keyPath, 'utf8');\n  } catch (error) {\n    console.error('读取私钥文件失败:', error);\n    throw new Error('私钥文件不存在或无法读取，请确保证书文件已正确配置');\n  }\n}\n\n// 生成微信支付 API v3 签名\nfunction generateV3Signature(method: string, url: string, timestamp: string, nonce: string, body: string): string {\n  // 构造签名串\n  const message = `${method}\\n${url}\\n${timestamp}\\n${nonce}\\n${body}\\n`;\n\n  try {\n    const privateKey = getPrivateKey();\n\n    // 使用 SHA256withRSA 签名\n    const sign = crypto.createSign('RSA-SHA256');\n    sign.update(message, 'utf8');\n    const signature = sign.sign(privateKey, 'base64');\n\n    console.log('=== 微信支付 API v3 签名调试 ===');\n    console.log('1. 签名方法:', method);\n    console.log('2. 请求URL:', url);\n    console.log('3. 时间戳:', timestamp);\n    console.log('4. 随机字符串:', nonce);\n    console.log('5. 请求体:', body);\n    console.log('6. 签名消息:', message);\n    console.log('7. 生成签名:', signature);\n    console.log('================================');\n\n    return signature;\n  } catch (error) {\n    console.error('生成签名失败:', error);\n    throw new Error('签名生成失败，请检查私钥文件');\n  }\n}\n\n// 生成 Authorization 头\nfunction generateAuthorizationHeader(method: string, url: string, body: string): string {\n  const timestamp = Math.floor(Date.now() / 1000).toString();\n  const nonce = generateNonceStr();\n  const signature = generateV3Signature(method, url, timestamp, nonce, body);\n\n  const serialNumber = WECHAT_CONFIG.certSerialNumber || 'CERT_SERIAL_NUMBER_PLACEHOLDER';\n\n  return `WECHATPAY2-SHA256-RSA2048 mchid=\"${WECHAT_CONFIG.mchId}\",nonce_str=\"${nonce}\",signature=\"${signature}\",timestamp=\"${timestamp}\",serial_no=\"${serialNumber}\"`;\n}\n\n// 验证微信支付 API v3 回调签名\nexport function verifyV3CallbackSignature(\n  timestamp: string,\n  nonce: string,\n  body: string,\n  signature: string,\n  serialNumber: string\n): boolean {\n  try {\n    // 构造验签名串\n    const message = `${timestamp}\\n${nonce}\\n${body}\\n`;\n\n    // 这里应该使用微信支付平台证书来验证签名\n    // 为了简化，我们先返回 true，实际生产环境需要实现完整的证书验证\n    console.log('验证回调签名:', { timestamp, nonce, body: body.substring(0, 100), signature, serialNumber });\n\n    return true; // 临时返回 true，实际需要用平台证书验证\n  } catch (error) {\n    console.error('验证回调签名失败:', error);\n    return false;\n  }\n}\n\n// 解密微信支付 API v3 回调数据\nfunction decryptV3CallbackData(\n  ciphertext: string,\n  associatedData: string,\n  nonce: string\n): string {\n  try {\n    const key = WECHAT_CONFIG.apiV3Key;\n\n    // 使用 AES-256-GCM 解密\n    const decipher = crypto.createDecipherGCM('aes-256-gcm', Buffer.from(key, 'utf8'));\n    decipher.setAAD(Buffer.from(associatedData, 'utf8'));\n\n    const ciphertextBuffer = Buffer.from(ciphertext, 'base64');\n    const tag = ciphertextBuffer.slice(-16);\n    const encrypted = ciphertextBuffer.slice(0, -16);\n\n    decipher.setAuthTag(tag);\n\n    let decrypted = decipher.update(encrypted, undefined, 'utf8');\n    decrypted += decipher.final('utf8');\n\n    return decrypted;\n  } catch (error) {\n    console.error('解密回调数据失败:', error);\n    throw new Error('解密回调数据失败');\n  }\n}\n\n// 创建微信支付 API v3 Native 订单\nexport async function createWechatPayOrder(\n  orderId: string,\n  amount: number, // 金额（分）\n  description: string,\n  clientIp: string = '127.0.0.1'\n): Promise<{ success: boolean; qrCodeUrl?: string; error?: string }> {\n  try {\n    // 构造请求参数\n    const requestData: WechatPayV3NativeRequest = {\n      appid: WECHAT_CONFIG.appId,\n      mchid: WECHAT_CONFIG.mchId,\n      description: description,\n      out_trade_no: orderId,\n      notify_url: WECHAT_CONFIG.notifyUrl,\n      amount: {\n        total: amount,\n        currency: 'CNY'\n      },\n      scene_info: {\n        payer_client_ip: clientIp\n      }\n    };\n\n    const requestBody = JSON.stringify(requestData);\n    const url = '/v3/pay/transactions/native';\n    const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;\n\n    // 生成 Authorization 头\n    const authorization = generateAuthorizationHeader('POST', url, requestBody);\n\n    console.log('=== 微信支付 API v3 请求调试 ===');\n    console.log('1. 请求URL:', fullUrl);\n    console.log('2. 请求体:', requestBody);\n    console.log('3. Authorization:', authorization);\n    console.log('================================');\n\n    // 发送请求\n    const response = await axios.post(fullUrl, requestBody, {\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'Authorization': authorization,\n        'User-Agent': 'Augment-Pay/1.0'\n      },\n      timeout: 10000,\n    });\n\n    console.log('微信支付 API v3 响应:', response.data);\n\n    const responseData: WechatPayV3NativeResponse = response.data;\n\n    return {\n      success: true,\n      qrCodeUrl: responseData.code_url\n    };\n\n  } catch (error) {\n    console.error('创建微信支付订单失败:', error);\n\n    if (axios.isAxiosError(error)) {\n      const errorMessage = error.response?.data?.message || error.message;\n      console.error('API 错误详情:', error.response?.data);\n\n      return {\n        success: false,\n        error: `微信支付API错误: ${errorMessage}`\n      };\n    }\n\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '创建支付订单失败'\n    };\n  }\n}\n\n// 查询微信支付 API v3 订单状态\nexport async function queryWechatPayOrder(\n  orderId: string\n): Promise<{ success: boolean; isPaid?: boolean; error?: string }> {\n  try {\n    const url = `/v3/pay/transactions/out-trade-no/${orderId}?mchid=${WECHAT_CONFIG.mchId}`;\n    const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;\n\n    // 生成 Authorization 头（GET 请求，body 为空字符串）\n    const authorization = generateAuthorizationHeader('GET', url, '');\n\n    const response = await axios.get(fullUrl, {\n      headers: {\n        'Accept': 'application/json',\n        'Authorization': authorization,\n        'User-Agent': 'Augment-Pay/1.0'\n      },\n      timeout: 10000,\n    });\n\n    const responseData: WechatPayV3QueryResponse = response.data;\n\n    console.log('查询订单响应:', responseData);\n\n    // 检查支付状态\n    const isPaid = responseData.trade_state === 'SUCCESS';\n\n    return {\n      success: true,\n      isPaid\n    };\n\n  } catch (error) {\n    console.error('查询微信支付订单状态失败:', error);\n\n    if (axios.isAxiosError(error)) {\n      const errorMessage = error.response?.data?.message || error.message;\n      console.error('查询API错误详情:', error.response?.data);\n\n      return {\n        success: false,\n        error: `查询订单失败: ${errorMessage}`\n      };\n    }\n\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '查询支付状态失败'\n    };\n  }\n}\n\n// 处理微信支付 API v3 回调通知\nexport function parseV3WechatNotification(\n  requestBody: string,\n  signature: string,\n  timestamp: string,\n  nonce: string,\n  serialNumber: string\n): {\n  success: boolean;\n  data?: any;\n  error?: string;\n} {\n  try {\n    // 验证签名\n    if (!verifyV3CallbackSignature(timestamp, nonce, requestBody, signature, serialNumber)) {\n      return {\n        success: false,\n        error: '签名验证失败'\n      };\n    }\n\n    const notifyData: WechatPayV3NotifyRequest = JSON.parse(requestBody);\n\n    // 解密回调数据\n    const decryptedData = decryptV3CallbackData(\n      notifyData.resource.ciphertext,\n      notifyData.resource.associated_data,\n      notifyData.resource.nonce\n    );\n\n    const paymentData = JSON.parse(decryptedData);\n\n    return {\n      success: true,\n      data: paymentData\n    };\n\n  } catch (error) {\n    console.error('解析微信支付 API v3 回调失败:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : '解析回调数据失败'\n    };\n  }\n}\n\n// 生成微信支付 API v3 回调响应\nexport function generateV3WechatNotificationResponse(success: boolean, message?: string): any {\n  if (success) {\n    return { code: 'SUCCESS', message: message || 'OK' };\n  } else {\n    return { code: 'FAIL', message: message || 'FAIL' };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,iBAAiB;AACjB,MAAM,gBAAgB;IACpB,OAAO,QAAQ,GAAG,CAAC,aAAa;IAChC,OAAO,QAAQ,GAAG,CAAC,aAAa;IAChC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;IACvC,UAAU,QAAQ,GAAG,CAAC,gBAAgB;IACtC,SAAS,QAAQ,GAAG,CAAC,eAAe;IACpC,kBAAkB,QAAQ,GAAG,CAAC,yBAAyB;IACvD,WAAW,6DAAmC,mBAAmB,CAAC;IAClE,SAAS;AACX;AA8DA,SAAS;AACT,SAAS;IACP,IAAI;QACF,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,cAAc,OAAO;QACjE,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,SAAS;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,mBAAmB;AACnB,SAAS,oBAAoB,MAAc,EAAE,GAAW,EAAE,SAAiB,EAAE,KAAa,EAAE,IAAY;IACtG,QAAQ;IACR,MAAM,UAAU,GAAG,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;IAEtE,IAAI;QACF,MAAM,aAAa;QAEnB,sBAAsB;QACtB,MAAM,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC;QAC/B,KAAK,MAAM,CAAC,SAAS;QACrB,MAAM,YAAY,KAAK,IAAI,CAAC,YAAY;QAExC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC,aAAa;QACzB,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,aAAa;QACzB,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC;QAEZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,qBAAqB;AACrB,SAAS,4BAA4B,MAAc,EAAE,GAAW,EAAE,IAAY;IAC5E,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,MAAM,QAAQ;IACxD,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;IAC7B,MAAM,YAAY,oBAAoB,QAAQ,KAAK,WAAW,OAAO;IAErE,MAAM,eAAe,cAAc,gBAAgB,IAAI;IAEvD,OAAO,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC,aAAa,EAAE,MAAM,aAAa,EAAE,UAAU,aAAa,EAAE,UAAU,aAAa,EAAE,aAAa,CAAC,CAAC;AACtK;AAGO,SAAS,0BACd,SAAiB,EACjB,KAAa,EACb,IAAY,EACZ,SAAiB,EACjB,YAAoB;IAEpB,IAAI;QACF,SAAS;QACT,MAAM,UAAU,GAAG,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;QAEnD,sBAAsB;QACtB,oCAAoC;QACpC,QAAQ,GAAG,CAAC,WAAW;YAAE;YAAW;YAAO,MAAM,KAAK,SAAS,CAAC,GAAG;YAAM;YAAW;QAAa;QAEjG,OAAO,MAAM,wBAAwB;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAEA,qBAAqB;AACrB,SAAS,sBACP,UAAkB,EAClB,cAAsB,EACtB,KAAa;IAEb,IAAI;QACF,MAAM,MAAM,cAAc,QAAQ;QAElC,oBAAoB;QACpB,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,iBAAiB,CAAC,eAAe,OAAO,IAAI,CAAC,KAAK;QAC1E,SAAS,MAAM,CAAC,OAAO,IAAI,CAAC,gBAAgB;QAE5C,MAAM,mBAAmB,OAAO,IAAI,CAAC,YAAY;QACjD,MAAM,MAAM,iBAAiB,KAAK,CAAC,CAAC;QACpC,MAAM,YAAY,iBAAiB,KAAK,CAAC,GAAG,CAAC;QAE7C,SAAS,UAAU,CAAC;QAEpB,IAAI,YAAY,SAAS,MAAM,CAAC,WAAW,WAAW;QACtD,aAAa,SAAS,KAAK,CAAC;QAE5B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,qBACpB,OAAe,EACf,MAAc,EACd,WAAmB,EACnB,WAAmB,WAAW;IAE9B,IAAI;QACF,SAAS;QACT,MAAM,cAAwC;YAC5C,OAAO,cAAc,KAAK;YAC1B,OAAO,cAAc,KAAK;YAC1B,aAAa;YACb,cAAc;YACd,YAAY,cAAc,SAAS;YACnC,QAAQ;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,YAAY;gBACV,iBAAiB;YACnB;QACF;QAEA,MAAM,cAAc,KAAK,SAAS,CAAC;QACnC,MAAM,MAAM;QACZ,MAAM,UAAU,GAAG,cAAc,OAAO,GAAG,KAAK;QAEhD,qBAAqB;QACrB,MAAM,gBAAgB,4BAA4B,QAAQ,KAAK;QAE/D,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,aAAa;QACzB,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,qBAAqB;QACjC,QAAQ,GAAG,CAAC;QAEZ,OAAO;QACP,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,SAAS,aAAa;YACtD,SAAS;gBACP,gBAAgB;gBAChB,UAAU;gBACV,iBAAiB;gBACjB,cAAc;YAChB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,mBAAmB,SAAS,IAAI;QAE5C,MAAM,eAA0C,SAAS,IAAI;QAE7D,OAAO;YACL,SAAS;YACT,WAAW,aAAa,QAAQ;QAClC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAE7B,IAAI,uIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;YACnE,QAAQ,KAAK,CAAC,aAAa,MAAM,QAAQ,EAAE;YAE3C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,WAAW,EAAE,cAAc;YACrC;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,oBACpB,OAAe;IAEf,IAAI;QACF,MAAM,MAAM,CAAC,kCAAkC,EAAE,QAAQ,OAAO,EAAE,cAAc,KAAK,EAAE;QACvF,MAAM,UAAU,GAAG,cAAc,OAAO,GAAG,KAAK;QAEhD,wCAAwC;QACxC,MAAM,gBAAgB,4BAA4B,OAAO,KAAK;QAE9D,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,SAAS;YACxC,SAAS;gBACP,UAAU;gBACV,iBAAiB;gBACjB,cAAc;YAChB;YACA,SAAS;QACX;QAEA,MAAM,eAAyC,SAAS,IAAI;QAE5D,QAAQ,GAAG,CAAC,WAAW;QAEvB,SAAS;QACT,MAAM,SAAS,aAAa,WAAW,KAAK;QAE5C,OAAO;YACL,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,IAAI,uIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;YACnE,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE;YAE5C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,QAAQ,EAAE,cAAc;YAClC;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,SAAS,0BACd,WAAmB,EACnB,SAAiB,EACjB,SAAiB,EACjB,KAAa,EACb,YAAoB;IAMpB,IAAI;QACF,OAAO;QACP,IAAI,CAAC,0BAA0B,WAAW,OAAO,aAAa,WAAW,eAAe;YACtF,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,aAAuC,KAAK,KAAK,CAAC;QAExD,SAAS;QACT,MAAM,gBAAgB,sBACpB,WAAW,QAAQ,CAAC,UAAU,EAC9B,WAAW,QAAQ,CAAC,eAAe,EACnC,WAAW,QAAQ,CAAC,KAAK;QAG3B,MAAM,cAAc,KAAK,KAAK,CAAC;QAE/B,OAAO;YACL,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,SAAS,qCAAqC,OAAgB,EAAE,OAAgB;IACrF,IAAI,SAAS;QACX,OAAO;YAAE,MAAM;YAAW,SAAS,WAAW;QAAK;IACrD,OAAO;QACL,OAAO;YAAE,MAAM;YAAQ,SAAS,WAAW;QAAO;IACpD;AACF", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/src/app/api/orders/create/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { OrderService, PaymentService } from '@/lib/database';\nimport { createWechatPayOrder } from '@/lib/wechat-pay';\nimport { generateOrderId, isValidEmail, amountToFen } from '@/lib/utils';\nimport { CreateOrderRequest, ApiResponse, CreateOrderResponse } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: CreateOrderRequest = await request.json();\n    const { userEmail } = body;\n\n    // 验证请求参数\n    if (!userEmail) {\n      return NextResponse.json({\n        success: false,\n        error: '邮箱地址不能为空'\n      } as ApiResponse, { status: 400 });\n    }\n\n    if (!isValidEmail(userEmail)) {\n      return NextResponse.json({\n        success: false,\n        error: '邮箱地址格式不正确'\n      } as ApiResponse, { status: 400 });\n    }\n\n    // 获取价格配置\n    const price = parseFloat(process.env.AUGMENT_ACCOUNT_PRICE || '99');\n    const amountInFen = amountToFen(price);\n\n    // 创建订单\n    const order = await OrderService.createOrder(userEmail, amountInFen);\n\n    // 获取客户端IP\n    const clientIp = request.headers.get('x-forwarded-for') || \n                     request.headers.get('x-real-ip') || \n                     '127.0.0.1';\n\n    // 创建微信支付订单\n    const wechatPayResult = await createWechatPayOrder(\n      order.id,\n      amountInFen,\n      'Augment 账号购买',\n      clientIp\n    );\n\n    if (!wechatPayResult.success) {\n      // 如果微信支付创建失败，更新订单状态为失败\n      await OrderService.updateOrderStatus(order.id, 'FAILED');\n\n      console.error('微信支付创建失败:', wechatPayResult.error);\n\n      return NextResponse.json({\n        success: false,\n        error: wechatPayResult.error || '创建支付订单失败'\n      } as ApiResponse, { status: 500 });\n    }\n\n    // 创建支付记录\n    await PaymentService.createPayment(\n      order.id,\n      amountInFen,\n      wechatPayResult.qrCodeUrl\n    );\n\n    // 返回成功响应\n    const response: CreateOrderResponse = {\n      orderId: order.id,\n      qrCodeUrl: wechatPayResult.qrCodeUrl!,\n      amount: price\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: response\n    } as ApiResponse<CreateOrderResponse>);\n\n  } catch (error) {\n    console.error('创建订单失败:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: '服务器内部错误'\n    } as ApiResponse, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAA2B,MAAM,QAAQ,IAAI;QACnD,MAAM,EAAE,SAAS,EAAE,GAAG;QAEtB,SAAS;QACT,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAkB;gBAAE,QAAQ;YAAI;QAClC;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,YAAY;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAkB;gBAAE,QAAQ;YAAI;QAClC;QAEA,SAAS;QACT,MAAM,QAAQ,WAAW,QAAQ,GAAG,CAAC,qBAAqB,IAAI;QAC9D,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;QAEhC,OAAO;QACP,MAAM,QAAQ,MAAM,wHAAA,CAAA,eAAY,CAAC,WAAW,CAAC,WAAW;QAExD,UAAU;QACV,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;QAEjB,WAAW;QACX,MAAM,kBAAkB,MAAM,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAC/C,MAAM,EAAE,EACR,aACA,gBACA;QAGF,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,uBAAuB;YACvB,MAAM,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE;YAE/C,QAAQ,KAAK,CAAC,aAAa,gBAAgB,KAAK;YAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO,gBAAgB,KAAK,IAAI;YAClC,GAAkB;gBAAE,QAAQ;YAAI;QAClC;QAEA,SAAS;QACT,MAAM,wHAAA,CAAA,iBAAc,CAAC,aAAa,CAChC,MAAM,EAAE,EACR,aACA,gBAAgB,SAAS;QAG3B,SAAS;QACT,MAAM,WAAgC;YACpC,SAAS,MAAM,EAAE;YACjB,WAAW,gBAAgB,SAAS;YACpC,QAAQ;QACV;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QAEzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAkB;YAAE,QAAQ;QAAI;IAClC;AACF", "debugId": null}}]}