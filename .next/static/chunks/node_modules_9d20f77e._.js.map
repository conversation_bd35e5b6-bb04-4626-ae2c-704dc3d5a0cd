{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/native.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,OAAO,WAAW,eAAe,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC;uCACjF;IAAE;AAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI;AACJ,MAAM,QAAQ,IAAI,WAAW;AACd,SAAS;IACpB,IAAI,CAAC,iBAAiB;QAClB,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,eAAe,EAAE;YAC1D,MAAM,IAAI,MAAM;QACpB;QACA,kBAAkB,OAAO,eAAe,CAAC,IAAI,CAAC;IAClD;IACA,OAAO,gBAAgB;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,0JAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG;QAAE,SAAA,iEAAS;IAC1C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG;QAAE,SAAA,iEAAS;IAC7B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,CAAA,GAAA,6JAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/uuid/dist/esm-browser/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;QAKG;IAJ/B,IAAI,2JAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,2JAAA,CAAA,UAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;QACT,iBAAA;IAAb,MAAM,OAAO,CAAA,OAAA,CAAA,kBAAA,QAAQ,MAAM,cAAd,6BAAA,mBAAkB,eAAA,QAAQ,GAAG,cAAX,mCAAA,kBAAA,sBAAlB,kBAAA,OAAqC,CAAA,GAAA,wJAAA,CAAA,UAAG,AAAD;IACpD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,AAAC,mBAA4B,OAAV,QAAO,KAAe,OAAZ,SAAS,IAAG;QAClE;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,8JAAA,CAAA,kBAAe,AAAD,EAAE;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/can-promise.js"], "sourcesContent": ["// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,0BAA0B;AAC1B,oDAAoD;AAEpD,OAAO,OAAO,GAAG;IACf,OAAO,OAAO,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,IAAI;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/utils.js"], "sourcesContent": ["let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,MAAM,kBAAkB;IACtB;IACA;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAC1C;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAC7C;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACtD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACvD;AAED;;;;;CAKC,GACD,QAAQ,aAAa,GAAG,SAAS,cAAe,OAAO;IACrD,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAC9B,IAAI,UAAU,KAAK,UAAU,IAAI,MAAM,IAAI,MAAM;IACjD,OAAO,UAAU,IAAI;AACvB;AAEA;;;;;CAKC,GACD,QAAQ,uBAAuB,GAAG,SAAS,wBAAyB,OAAO;IACzE,OAAO,eAAe,CAAC,QAAQ;AACjC;AAEA;;;;;CAKC,GACD,QAAQ,WAAW,GAAG,SAAU,IAAI;IAClC,IAAI,QAAQ;IAEZ,MAAO,SAAS,EAAG;QACjB;QACA,UAAU;IACZ;IAEA,OAAO;AACT;AAEA,QAAQ,iBAAiB,GAAG,SAAS,kBAAmB,CAAC;IACvD,IAAI,OAAO,MAAM,YAAY;QAC3B,MAAM,IAAI,MAAM;IAClB;IAEA,iBAAiB;AACnB;AAEA,QAAQ,kBAAkB,GAAG;IAC3B,OAAO,OAAO,mBAAmB;AACnC;AAEA,QAAQ,MAAM,GAAG,SAAS,OAAQ,KAAK;IACrC,OAAO,eAAe;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/error-correction-level.js"], "sourcesContent": ["exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n"], "names": [], "mappings": "AAAA,QAAQ,CAAC,GAAG;IAAE,KAAK;AAAE;AACrB,QAAQ,CAAC,GAAG;IAAE,KAAK;AAAE;AACrB,QAAQ,CAAC,GAAG;IAAE,KAAK;AAAE;AACrB,QAAQ,CAAC,GAAG;IAAE,KAAK;AAAE;AAErB,SAAS,WAAY,MAAM;IACzB,IAAI,OAAO,WAAW,UAAU;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,OAAO,WAAW;IAEhC,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO,QAAQ,CAAC;QAElB,KAAK;QACL,KAAK;YACH,OAAO,QAAQ,CAAC;QAElB,KAAK;QACL,KAAK;YACH,OAAO,QAAQ,CAAC;QAElB,KAAK;QACL,KAAK;YACH,OAAO,QAAQ,CAAC;QAElB;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;AACF;AAEA,QAAQ,OAAO,GAAG,SAAS,QAAS,KAAK;IACvC,OAAO,SAAS,OAAO,MAAM,GAAG,KAAK,eACnC,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG;AAClC;AAEA,QAAQ,IAAI,GAAG,SAAS,KAAM,KAAK,EAAE,YAAY;IAC/C,IAAI,QAAQ,OAAO,CAAC,QAAQ;QAC1B,OAAO;IACT;IAEA,IAAI;QACF,OAAO,WAAW;IACpB,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/bit-buffer.js"], "sourcesContent": ["function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n"], "names": [], "mappings": "AAAA,SAAS;IACP,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,MAAM,GAAG;AAChB;AAEA,UAAU,SAAS,GAAG;IAEpB,KAAK,SAAU,KAAK;QAClB,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ;QACpC,OAAO,CAAC,AAAC,IAAI,CAAC,MAAM,CAAC,SAAS,KAAM,IAAI,QAAQ,IAAM,CAAC,MAAM;IAC/D;IAEA,KAAK,SAAU,GAAG,EAAE,MAAM;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAI,CAAC,MAAM,CAAC,CAAC,AAAC,QAAS,SAAS,IAAI,IAAM,CAAC,MAAM;QACnD;IACF;IAEA,iBAAiB;QACf,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,SAAU,GAAG;QACnB,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;QAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,UAAU;YAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB;QAEA,IAAI,KAAK;YACP,IAAI,CAAC,MAAM,CAAC,SAAS,IAAK,SAAU,IAAI,CAAC,MAAM,GAAG;QACpD;QAEA,IAAI,CAAC,MAAM;IACb;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/bit-matrix.js"], "sourcesContent": ["/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n"], "names": [], "mappings": "AAAA;;;;CAIC,GACD,SAAS,UAAW,IAAI;IACtB,IAAI,CAAC,QAAQ,OAAO,GAAG;QACrB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,OAAO;IAClC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,OAAO;AAC3C;AAEA;;;;;;;;CAQC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ;IAC3D,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,GAAG;IAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;IACnB,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;AAC1C;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG;IAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI;AACzC;AAEA;;;;;;;CAOC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;IACjD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI;AACtC;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG,EAAE,GAAG;IACjD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI;AAChD;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/alignment-pattern.js"], "sourcesContent": ["/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED,MAAM,gBAAgB,qGAAmB,aAAa;AAEtD;;;;;;;;;;;;;CAaC,GACD,QAAQ,eAAe,GAAG,SAAS,gBAAiB,OAAO;IACzD,IAAI,YAAY,GAAG,OAAO,EAAE;IAE5B,MAAM,WAAW,KAAK,KAAK,CAAC,UAAU,KAAK;IAC3C,MAAM,OAAO,cAAc;IAC3B,MAAM,YAAY,SAAS,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK;IACpF,MAAM,YAAY;QAAC,OAAO;KAAE,CAAC,kCAAkC;;IAE/D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,GAAG,IAAK;QACrC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG;IACpC;IAEA,UAAU,IAAI,CAAC,IAAG,0BAA0B;IAE5C,OAAO,UAAU,OAAO;AAC1B;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,QAAQ,YAAY,GAAG,SAAS,aAAc,OAAO;IACnD,MAAM,SAAS,EAAE;IACjB,MAAM,MAAM,QAAQ,eAAe,CAAC;IACpC,MAAM,YAAY,IAAI,MAAM;IAE5B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,kDAAkD;YAClD,IAAI,AAAC,MAAM,KAAK,MAAM,KACjB,MAAM,KAAK,MAAM,YAAY,KAC7B,MAAM,YAAY,KAAK,MAAM,GAAI;gBACpC;YACF;YAEA,OAAO,IAAI,CAAC;gBAAC,GAAG,CAAC,EAAE;gBAAE,GAAG,CAAC,EAAE;aAAC;QAC9B;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/finder-pattern.js"], "sourcesContent": ["const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n"], "names": [], "mappings": "AAAA,MAAM,gBAAgB,qGAAmB,aAAa;AACtD,MAAM,sBAAsB;AAE5B;;;;;;CAMC,GACD,QAAQ,YAAY,GAAG,SAAS,aAAc,OAAO;IACnD,MAAM,OAAO,cAAc;IAE3B,OAAO;QACL,WAAW;QACX;YAAC;YAAG;SAAE;QACN,YAAY;QACZ;YAAC,OAAO;YAAqB;SAAE;QAC/B,cAAc;QACd;YAAC;YAAG,OAAO;SAAoB;KAChC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/mask-pattern.js"], "sourcesContent": ["/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GACD,QAAQ,QAAQ,GAAG;IACjB,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;AACd;AAEA;;;CAGC,GACD,MAAM,gBAAgB;IACpB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA;;;;;CAKC,GACD,QAAQ,OAAO,GAAG,SAAS,QAAS,IAAI;IACtC,OAAO,QAAQ,QAAQ,SAAS,MAAM,CAAC,MAAM,SAAS,QAAQ,KAAK,QAAQ;AAC7E;AAEA;;;;;;CAMC,GACD,QAAQ,IAAI,GAAG,SAAS,KAAM,KAAK;IACjC,OAAO,QAAQ,OAAO,CAAC,SAAS,SAAS,OAAO,MAAM;AACxD;AAEA;;;;;;AAMA,GACA,QAAQ,YAAY,GAAG,SAAS,aAAc,IAAI;IAChD,MAAM,OAAO,KAAK,IAAI;IACtB,IAAI,SAAS;IACb,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IACd,IAAI,UAAU;IAEd,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;QACnC,eAAe,eAAe;QAC9B,UAAU,UAAU;QAEpB,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;YACnC,IAAI,SAAS,KAAK,GAAG,CAAC,KAAK;YAC3B,IAAI,WAAW,SAAS;gBACtB;YACF,OAAO;gBACL,IAAI,gBAAgB,GAAG,UAAU,cAAc,EAAE,GAAG,CAAC,eAAe,CAAC;gBACrE,UAAU;gBACV,eAAe;YACjB;YAEA,SAAS,KAAK,GAAG,CAAC,KAAK;YACvB,IAAI,WAAW,SAAS;gBACtB;YACF,OAAO;gBACL,IAAI,gBAAgB,GAAG,UAAU,cAAc,EAAE,GAAG,CAAC,eAAe,CAAC;gBACrE,UAAU;gBACV,eAAe;YACjB;QACF;QAEA,IAAI,gBAAgB,GAAG,UAAU,cAAc,EAAE,GAAG,CAAC,eAAe,CAAC;QACrE,IAAI,gBAAgB,GAAG,UAAU,cAAc,EAAE,GAAG,CAAC,eAAe,CAAC;IACvE;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,QAAQ,YAAY,GAAG,SAAS,aAAc,IAAI;IAChD,MAAM,OAAO,KAAK,IAAI;IACtB,IAAI,SAAS;IAEb,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,GAAG,MAAO;QACvC,IAAK,IAAI,MAAM,GAAG,MAAM,OAAO,GAAG,MAAO;YACvC,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,OACzB,KAAK,GAAG,CAAC,KAAK,MAAM,KACpB,KAAK,GAAG,CAAC,MAAM,GAAG,OAClB,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM;YAE1B,IAAI,SAAS,KAAK,SAAS,GAAG;QAChC;IACF;IAEA,OAAO,SAAS,cAAc,EAAE;AAClC;AAEA;;;;;CAKC,GACD,QAAQ,YAAY,GAAG,SAAS,aAAc,IAAI;IAChD,MAAM,OAAO,KAAK,IAAI;IACtB,IAAI,SAAS;IACb,IAAI,UAAU;IACd,IAAI,UAAU;IAEd,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;QACnC,UAAU,UAAU;QACpB,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;YACnC,UAAU,AAAE,WAAW,IAAK,QAAS,KAAK,GAAG,CAAC,KAAK;YACnD,IAAI,OAAO,MAAM,CAAC,YAAY,SAAS,YAAY,KAAK,GAAG;YAE3D,UAAU,AAAE,WAAW,IAAK,QAAS,KAAK,GAAG,CAAC,KAAK;YACnD,IAAI,OAAO,MAAM,CAAC,YAAY,SAAS,YAAY,KAAK,GAAG;QAC7D;IACF;IAEA,OAAO,SAAS,cAAc,EAAE;AAClC;AAEA;;;;;;;CAOC,GACD,QAAQ,YAAY,GAAG,SAAS,aAAc,IAAI;IAChD,IAAI,YAAY;IAChB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK,aAAa,KAAK,IAAI,CAAC,EAAE;IAEhE,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,AAAC,YAAY,MAAM,eAAgB,KAAK;IAErE,OAAO,IAAI,cAAc,EAAE;AAC7B;AAEA;;;;;;;CAOC,GACD,SAAS,UAAW,WAAW,EAAE,CAAC,EAAE,CAAC;IACnC,OAAQ;QACN,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;QACzD,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,IAAI,MAAM;QACnD,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,IAAI,MAAM;QACnD,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;QACzD,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,MAAM;QACzF,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,AAAC,IAAI,IAAK,IAAI,AAAC,IAAI,IAAK,MAAM;QACvE,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,CAAC,AAAC,IAAI,IAAK,IAAI,AAAC,IAAI,IAAK,CAAC,IAAI,MAAM;QAC7E,KAAK,QAAQ,QAAQ,CAAC,UAAU;YAAE,OAAO,CAAC,AAAC,IAAI,IAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;QAE7E;YAAS,MAAM,IAAI,MAAM,qBAAqB;IAChD;AACF;AAEA;;;;;CAKC,GACD,QAAQ,SAAS,GAAG,SAAS,UAAW,OAAO,EAAE,IAAI;IACnD,MAAM,OAAO,KAAK,IAAI;IAEtB,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;QACnC,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;YACnC,IAAI,KAAK,UAAU,CAAC,KAAK,MAAM;YAC/B,KAAK,GAAG,CAAC,KAAK,KAAK,UAAU,SAAS,KAAK;QAC7C;IACF;AACF;AAEA;;;;;CAKC,GACD,QAAQ,WAAW,GAAG,SAAS,YAAa,IAAI,EAAE,eAAe;IAC/D,MAAM,cAAc,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE,MAAM;IACxD,IAAI,cAAc;IAClB,IAAI,eAAe;IAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,gBAAgB;QAChB,QAAQ,SAAS,CAAC,GAAG;QAErB,oBAAoB;QACpB,MAAM,UACJ,QAAQ,YAAY,CAAC,QACrB,QAAQ,YAAY,CAAC,QACrB,QAAQ,YAAY,CAAC,QACrB,QAAQ,YAAY,CAAC;QAEvB,+BAA+B;QAC/B,QAAQ,SAAS,CAAC,GAAG;QAErB,IAAI,UAAU,cAAc;YAC1B,eAAe;YACf,cAAc;QAChB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/error-correction-code.js"], "sourcesContent": ["const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,MAAM,kBAAkB;IACxB,aAAa;IACX;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAG;IACT;IAAG;IAAG;IAAI;IACV;IAAG;IAAG;IAAI;IACV;IAAG;IAAG;IAAI;IACV;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAG;IAAI;IAAI;IACX;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;CACb;AAED,MAAM,qBAAqB;IAC3B,aAAa;IACX;IAAG;IAAI;IAAI;IACX;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAI;IACZ;IAAI;IAAI;IAAK;IACb;IAAI;IAAI;IAAK;IACb;IAAI;IAAK;IAAK;IACd;IAAI;IAAK;IAAK;IACd;IAAI;IAAK;IAAK;IACd;IAAI;IAAK;IAAK;IACd;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAK;IACf;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAK;IAAM;IAChB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;IACjB;IAAK;IAAM;IAAM;CAClB;AAED;;;;;;;CAOC,GACD,QAAQ,cAAc,GAAG,SAAS,eAAgB,OAAO,EAAE,oBAAoB;IAC7E,OAAQ;QACN,KAAK,QAAQ,CAAC;YACZ,OAAO,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAC/C,KAAK,QAAQ,CAAC;YACZ,OAAO,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAC/C,KAAK,QAAQ,CAAC;YACZ,OAAO,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAC/C,KAAK,QAAQ,CAAC;YACZ,OAAO,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAC/C;YACE,OAAO;IACX;AACF;AAEA;;;;;;;CAOC,GACD,QAAQ,sBAAsB,GAAG,SAAS,uBAAwB,OAAO,EAAE,oBAAoB;IAC7F,OAAQ;QACN,KAAK,QAAQ,CAAC;YACZ,OAAO,kBAAkB,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAClD,KAAK,QAAQ,CAAC;YACZ,OAAO,kBAAkB,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAClD,KAAK,QAAQ,CAAC;YACZ,OAAO,kBAAkB,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAClD,KAAK,QAAQ,CAAC;YACZ,OAAO,kBAAkB,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;QAClD;YACE,OAAO;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/galois-field.js"], "sourcesContent": ["const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n"], "names": [], "mappings": "AAAA,MAAM,YAAY,IAAI,WAAW;AACjC,MAAM,YAAY,IAAI,WAAW;AAS/B,CAAA,SAAS;IACT,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,SAAS,CAAC,EAAE,GAAG;QACf,SAAS,CAAC,EAAE,GAAG;QAEf,MAAM,GAAE,gBAAgB;QAExB,+EAA+E;QAC/E,iFAAiF;QACjF,IAAI,IAAI,OAAO;YACb,KAAK;QACP;IACF;IAEA,0FAA0F;IAC1F,0FAA0F;IAC1F,4BAA4B;IAC5B,mBAAmB;IACnB,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAK;QAC9B,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,IAAI;IACnC;AACF,CAAA;AAEA;;;;;CAKC,GACD,QAAQ,GAAG,GAAG,SAAS,IAAK,CAAC;IAC3B,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,SAAS,IAAI;IACxC,OAAO,SAAS,CAAC,EAAE;AACrB;AAEA;;;;;CAKC,GACD,QAAQ,GAAG,GAAG,SAAS,IAAK,CAAC;IAC3B,OAAO,SAAS,CAAC,EAAE;AACrB;AAEA;;;;;;CAMC,GACD,QAAQ,GAAG,GAAG,SAAS,IAAK,CAAC,EAAE,CAAC;IAC9B,IAAI,MAAM,KAAK,MAAM,GAAG,OAAO;IAE/B,yFAAyF;IACzF,0BAA0B;IAC1B,OAAO,SAAS,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/polynomial.js"], "sourcesContent": ["const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n"], "names": [], "mappings": "AAAA,MAAM;AAEN;;;;;;CAMC,GACD,QAAQ,GAAG,GAAG,SAAS,IAAK,EAAE,EAAE,EAAE;IAChC,MAAM,QAAQ,IAAI,WAAW,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG;IAErD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;YAClC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QACrC;IACF;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,QAAQ,GAAG,GAAG,SAAS,IAAK,QAAQ,EAAE,OAAO;IAC3C,IAAI,SAAS,IAAI,WAAW;IAE5B,MAAO,AAAC,OAAO,MAAM,GAAG,QAAQ,MAAM,IAAK,EAAG;QAC5C,MAAM,QAAQ,MAAM,CAAC,EAAE;QAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE;QAClC;QAEA,oCAAoC;QACpC,IAAI,SAAS;QACb,MAAO,SAAS,OAAO,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,EAAG;QACvD,SAAS,OAAO,KAAK,CAAC;IACxB;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,QAAQ,oBAAoB,GAAG,SAAS,qBAAsB,MAAM;IAClE,IAAI,OAAO,IAAI,WAAW;QAAC;KAAE;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,OAAO,QAAQ,GAAG,CAAC,MAAM,IAAI,WAAW;YAAC;YAAG,GAAG,GAAG,CAAC;SAAG;IACxD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/reed-solomon-encoder.js"], "sourcesContent": ["const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,mBAAoB,MAAM;IACjC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,MAAM,GAAG;IAEd,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;AAC9C;AAEA;;;;;CAKC,GACD,mBAAmB,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,MAAM;IACnE,6CAA6C;IAC7C,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG,WAAW,oBAAoB,CAAC,IAAI,CAAC,MAAM;AAC5D;AAEA;;;;;CAKC,GACD,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAS,OAAQ,IAAI;IACzD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,mCAAmC;IACnC,yCAAyC;IACzC,MAAM,aAAa,IAAI,WAAW,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM;IAC3D,WAAW,GAAG,CAAC;IAEf,qFAAqF;IACrF,4BAA4B;IAC5B,MAAM,YAAY,WAAW,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO;IAEzD,wEAAwE;IACxE,oEAAoE;IACpE,qEAAqE;IACrE,MAAM,QAAQ,IAAI,CAAC,MAAM,GAAG,UAAU,MAAM;IAC5C,IAAI,QAAQ,GAAG;QACb,MAAM,OAAO,IAAI,WAAW,IAAI,CAAC,MAAM;QACvC,KAAK,GAAG,CAAC,WAAW;QAEpB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/version-check.js"], "sourcesContent": ["/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,QAAQ,OAAO,GAAG,SAAS,QAAS,OAAO;IACzC,OAAO,CAAC,MAAM,YAAY,WAAW,KAAK,WAAW;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/regex.js"], "sourcesContent": ["const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n"], "names": [], "mappings": "AAAA,MAAM,UAAU;AAChB,MAAM,eAAe;AACrB,IAAI,QAAQ,kDACV,mEACA,0DACA;AACF,QAAQ,MAAM,OAAO,CAAC,MAAM;AAE5B,MAAM,OAAO,+BAA+B,QAAQ;AAEpD,QAAQ,KAAK,GAAG,IAAI,OAAO,OAAO;AAClC,QAAQ,UAAU,GAAG,IAAI,OAAO,yBAAyB;AACzD,QAAQ,IAAI,GAAG,IAAI,OAAO,MAAM;AAChC,QAAQ,OAAO,GAAG,IAAI,OAAO,SAAS;AACtC,QAAQ,YAAY,GAAG,IAAI,OAAO,cAAc;AAEhD,MAAM,aAAa,IAAI,OAAO,MAAM,QAAQ;AAC5C,MAAM,eAAe,IAAI,OAAO,MAAM,UAAU;AAChD,MAAM,oBAAoB,IAAI,OAAO;AAErC,QAAQ,SAAS,GAAG,SAAS,UAAW,GAAG;IACzC,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,QAAQ,WAAW,GAAG,SAAS,YAAa,GAAG;IAC7C,OAAO,aAAa,IAAI,CAAC;AAC3B;AAEA,QAAQ,gBAAgB,GAAG,SAAS,iBAAkB,GAAG;IACvD,OAAO,kBAAkB,IAAI,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/mode.js"], "sourcesContent": ["const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN;;;;;;CAMC,GACD,QAAQ,OAAO,GAAG;IAChB,IAAI;IACJ,KAAK,KAAK;IACV,QAAQ;QAAC;QAAI;QAAI;KAAG;AACtB;AAEA;;;;;;;;CAQC,GACD,QAAQ,YAAY,GAAG;IACrB,IAAI;IACJ,KAAK,KAAK;IACV,QAAQ;QAAC;QAAG;QAAI;KAAG;AACrB;AAEA;;;;CAIC,GACD,QAAQ,IAAI,GAAG;IACb,IAAI;IACJ,KAAK,KAAK;IACV,QAAQ;QAAC;QAAG;QAAI;KAAG;AACrB;AAEA;;;;;;;;CAQC,GACD,QAAQ,KAAK,GAAG;IACd,IAAI;IACJ,KAAK,KAAK;IACV,QAAQ;QAAC;QAAG;QAAI;KAAG;AACrB;AAEA;;;;;CAKC,GACD,QAAQ,KAAK,GAAG;IACd,KAAK,CAAC;AACR;AAEA;;;;;;;CAOC,GACD,QAAQ,qBAAqB,GAAG,SAAS,sBAAuB,IAAI,EAAE,OAAO;IAC3E,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM,IAAI,MAAM,mBAAmB;IAErD,IAAI,CAAC,aAAa,OAAO,CAAC,UAAU;QAClC,MAAM,IAAI,MAAM,sBAAsB;IACxC;IAEA,IAAI,WAAW,KAAK,UAAU,IAAI,OAAO,KAAK,MAAM,CAAC,EAAE;SAClD,IAAI,UAAU,IAAI,OAAO,KAAK,MAAM,CAAC,EAAE;IAC5C,OAAO,KAAK,MAAM,CAAC,EAAE;AACvB;AAEA;;;;;CAKC,GACD,QAAQ,kBAAkB,GAAG,SAAS,mBAAoB,OAAO;IAC/D,IAAI,MAAM,WAAW,CAAC,UAAU,OAAO,QAAQ,OAAO;SACjD,IAAI,MAAM,gBAAgB,CAAC,UAAU,OAAO,QAAQ,YAAY;SAChE,IAAI,MAAM,SAAS,CAAC,UAAU,OAAO,QAAQ,KAAK;SAClD,OAAO,QAAQ,IAAI;AAC1B;AAEA;;;;;CAKC,GACD,QAAQ,QAAQ,GAAG,SAAS,SAAU,IAAI;IACxC,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,KAAK,EAAE;IACnC,MAAM,IAAI,MAAM;AAClB;AAEA;;;;;CAKC,GACD,QAAQ,OAAO,GAAG,SAAS,QAAS,IAAI;IACtC,OAAO,QAAQ,KAAK,GAAG,IAAI,KAAK,MAAM;AACxC;AAEA;;;;;CAKC,GACD,SAAS,WAAY,MAAM;IACzB,IAAI,OAAO,WAAW,UAAU;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,OAAO,WAAW;IAEhC,OAAQ;QACN,KAAK;YACH,OAAO,QAAQ,OAAO;QACxB,KAAK;YACH,OAAO,QAAQ,YAAY;QAC7B,KAAK;YACH,OAAO,QAAQ,KAAK;QACtB,KAAK;YACH,OAAO,QAAQ,IAAI;QACrB;YACE,MAAM,IAAI,MAAM,mBAAmB;IACvC;AACF;AAEA;;;;;;;CAOC,GACD,QAAQ,IAAI,GAAG,SAAS,KAAM,KAAK,EAAE,YAAY;IAC/C,IAAI,QAAQ,OAAO,CAAC,QAAQ;QAC1B,OAAO;IACT;IAEA,IAAI;QACF,OAAO,WAAW;IACpB,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/version.js"], "sourcesContent": ["const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,0DAA0D;AAC1D,MAAM,MAAM,AAAC,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAClG,MAAM,UAAU,MAAM,WAAW,CAAC;AAElC,SAAS,4BAA6B,IAAI,EAAE,MAAM,EAAE,oBAAoB;IACtE,IAAK,IAAI,iBAAiB,GAAG,kBAAkB,IAAI,iBAAkB;QACnE,IAAI,UAAU,QAAQ,WAAW,CAAC,gBAAgB,sBAAsB,OAAO;YAC7E,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,qBAAsB,IAAI,EAAE,OAAO;IAC1C,kDAAkD;IAClD,OAAO,KAAK,qBAAqB,CAAC,MAAM,WAAW;AACrD;AAEA,SAAS,0BAA2B,QAAQ,EAAE,OAAO;IACnD,IAAI,YAAY;IAEhB,SAAS,OAAO,CAAC,SAAU,IAAI;QAC7B,MAAM,eAAe,qBAAqB,KAAK,IAAI,EAAE;QACrD,aAAa,eAAe,KAAK,aAAa;IAChD;IAEA,OAAO;AACT;AAEA,SAAS,2BAA4B,QAAQ,EAAE,oBAAoB;IACjE,IAAK,IAAI,iBAAiB,GAAG,kBAAkB,IAAI,iBAAkB;QACnE,MAAM,SAAS,0BAA0B,UAAU;QACnD,IAAI,UAAU,QAAQ,WAAW,CAAC,gBAAgB,sBAAsB,KAAK,KAAK,GAAG;YACnF,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,QAAQ,IAAI,GAAG,SAAS,KAAM,KAAK,EAAE,YAAY;IAC/C,IAAI,aAAa,OAAO,CAAC,QAAQ;QAC/B,OAAO,SAAS,OAAO;IACzB;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,QAAQ,WAAW,GAAG,SAAS,YAAa,OAAO,EAAE,oBAAoB,EAAE,IAAI;IAC7E,IAAI,CAAC,aAAa,OAAO,CAAC,UAAU;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,2BAA2B;IAC3B,IAAI,OAAO,SAAS,aAAa,OAAO,KAAK,IAAI;IAEjD,qEAAqE;IACrE,MAAM,iBAAiB,MAAM,uBAAuB,CAAC;IAErD,6CAA6C;IAC7C,MAAM,mBAAmB,OAAO,sBAAsB,CAAC,SAAS;IAEhE,iCAAiC;IACjC,MAAM,yBAAyB,CAAC,iBAAiB,gBAAgB,IAAI;IAErE,IAAI,SAAS,KAAK,KAAK,EAAE,OAAO;IAEhC,MAAM,aAAa,yBAAyB,qBAAqB,MAAM;IAEvE,0CAA0C;IAC1C,OAAQ;QACN,KAAK,KAAK,OAAO;YACf,OAAO,KAAK,KAAK,CAAC,AAAC,aAAa,KAAM;QAExC,KAAK,KAAK,YAAY;YACpB,OAAO,KAAK,KAAK,CAAC,AAAC,aAAa,KAAM;QAExC,KAAK,KAAK,KAAK;YACb,OAAO,KAAK,KAAK,CAAC,aAAa;QAEjC,KAAK,KAAK,IAAI;QACd;YACE,OAAO,KAAK,KAAK,CAAC,aAAa;IACnC;AACF;AAEA;;;;;;;CAOC,GACD,QAAQ,qBAAqB,GAAG,SAAS,sBAAuB,IAAI,EAAE,oBAAoB;IACxF,IAAI;IAEJ,MAAM,MAAM,QAAQ,IAAI,CAAC,sBAAsB,QAAQ,CAAC;IAExD,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,OAAO,2BAA2B,MAAM;QAC1C;QAEA,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,OAAO;QACT;QAEA,MAAM,IAAI,CAAC,EAAE;IACf,OAAO;QACL,MAAM;IACR;IAEA,OAAO,4BAA4B,IAAI,IAAI,EAAE,IAAI,SAAS,IAAI;AAChE;AAEA;;;;;;;;;CASC,GACD,QAAQ,cAAc,GAAG,SAAS,eAAgB,OAAO;IACvD,IAAI,CAAC,aAAa,OAAO,CAAC,YAAY,UAAU,GAAG;QACjD,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,IAAI,WAAW;IAEnB,MAAO,MAAM,WAAW,CAAC,KAAK,WAAW,EAAG;QAC1C,KAAM,OAAQ,MAAM,WAAW,CAAC,KAAK;IACvC;IAEA,OAAO,AAAC,WAAW,KAAM;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/format-info.js"], "sourcesContent": ["const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,MAAM,MAAM,AAAC,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AACrF,MAAM,WAAW,AAAC,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AACtE,MAAM,UAAU,MAAM,WAAW,CAAC;AAElC;;;;;;;;;CASC,GACD,QAAQ,cAAc,GAAG,SAAS,eAAgB,oBAAoB,EAAE,IAAI;IAC1E,MAAM,OAAQ,AAAC,qBAAqB,GAAG,IAAI,IAAK;IAChD,IAAI,IAAI,QAAQ;IAEhB,MAAO,MAAM,WAAW,CAAC,KAAK,WAAW,EAAG;QAC1C,KAAM,OAAQ,MAAM,WAAW,CAAC,KAAK;IACvC;IAEA,2DAA2D;IAC3D,iEAAiE;IACjE,yCAAyC;IACzC,OAAO,CAAC,AAAC,QAAQ,KAAM,CAAC,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/numeric-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,YAAa,IAAI;IACxB,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO;IACxB,IAAI,CAAC,IAAI,GAAG,KAAK,QAAQ;AAC3B;AAEA,YAAY,aAAa,GAAG,SAAS,cAAe,MAAM;IACxD,OAAO,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,AAAC,SAAS,IAAM,AAAC,SAAS,IAAK,IAAI,IAAK,CAAC;AACjF;AAEA,YAAY,SAAS,CAAC,SAAS,GAAG,SAAS;IACzC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;AACzB;AAEA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAS;IAC7C,OAAO,YAAY,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;AACnD;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,MAAO,SAAS;IACrD,IAAI,GAAG,OAAO;IAEd,gEAAgE;IAChE,+DAA+D;IAC/D,IAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAG;QAC7C,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG;QAC5B,QAAQ,SAAS,OAAO;QAExB,UAAU,GAAG,CAAC,OAAO;IACvB;IAEA,mEAAmE;IACnE,yEAAyE;IACzE,MAAM,eAAe,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;IACxC,IAAI,eAAe,GAAG;QACpB,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QACzB,QAAQ,SAAS,OAAO;QAExB,UAAU,GAAG,CAAC,OAAO,eAAe,IAAI;IAC1C;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/alphanumeric-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n"], "names": [], "mappings": "AAAA,MAAM;AAEN;;;;;;;;CAQC,GACD,MAAM,kBAAkB;IACtB;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAC7C;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAC5D;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAC5D;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CACzC;AAED,SAAS,iBAAkB,IAAI;IAC7B,IAAI,CAAC,IAAI,GAAG,KAAK,YAAY;IAC7B,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,iBAAiB,aAAa,GAAG,SAAS,cAAe,MAAM;IAC7D,OAAO,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC;AACtD;AAEA,iBAAiB,SAAS,CAAC,SAAS,GAAG,SAAS;IAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;AACzB;AAEA,iBAAiB,SAAS,CAAC,aAAa,GAAG,SAAS;IAClD,OAAO,iBAAiB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;AACxD;AAEA,iBAAiB,SAAS,CAAC,KAAK,GAAG,SAAS,MAAO,SAAS;IAC1D,IAAI;IAEJ,kEAAkE;IAClE,sCAAsC;IACtC,IAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAG;QAC7C,iEAAiE;QACjE,IAAI,QAAQ,gBAAgB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI;QAEpD,kEAAkE;QAClE,SAAS,gBAAgB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QAEjD,iDAAiD;QACjD,UAAU,GAAG,CAAC,OAAO;IACvB;IAEA,mEAAmE;IACnE,kFAAkF;IAClF,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;QACxB,UAAU,GAAG,CAAC,gBAAgB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACvD;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/byte-data.js"], "sourcesContent": ["const Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    this.data = new TextEncoder().encode(data)\n  } else {\n    this.data = new Uint8Array(data)\n  }\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,SAAU,IAAI;IACrB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;IACrB,IAAI,OAAQ,SAAU,UAAU;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,MAAM,CAAC;IACvC,OAAO;QACL,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW;IAC7B;AACF;AAEA,SAAS,aAAa,GAAG,SAAS,cAAe,MAAM;IACrD,OAAO,SAAS;AAClB;AAEA,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS;IACtC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;AACzB;AAEA,SAAS,SAAS,CAAC,aAAa,GAAG,SAAS;IAC1C,OAAO,SAAS,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;AAChD;AAEA,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,SAAS;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,IAAK;QAChD,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;IAC9B;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/kanji-data.js"], "sourcesContent": ["const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AAEN,SAAS,UAAW,IAAI;IACtB,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK;IACtB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,UAAU,aAAa,GAAG,SAAS,cAAe,MAAM;IACtD,OAAO,SAAS;AAClB;AAEA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAS;IACvC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;AACzB;AAEA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAS;IAC3C,OAAO,UAAU,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;AACjD;AAEA,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,SAAS;IAC7C,IAAI;IAEJ,uFAAuF;IACvF,4DAA4D;IAC5D,8DAA8D;IAC9D,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;QACrC,IAAI,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAErC,8DAA8D;QAC9D,IAAI,SAAS,UAAU,SAAS,QAAQ;YACtC,uCAAuC;YACvC,SAAS;QAEX,6DAA6D;QAC7D,OAAO,IAAI,SAAS,UAAU,SAAS,QAAQ;YAC7C,uCAAuC;YACvC,SAAS;QACX,OAAO;YACL,MAAM,IAAI,MACR,6BAA6B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,OAC5C;QACJ;QAEA,mDAAmD;QACnD,4CAA4C;QAC5C,QAAQ,AAAC,CAAC,AAAC,UAAU,IAAK,IAAI,IAAI,OAAQ,CAAC,QAAQ,IAAI;QAEvD,2CAA2C;QAC3C,UAAU,GAAG,CAAC,OAAO;IACvB;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1795, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/segments.js"], "sourcesContent": ["const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN;;;;;CAKC,GACD,SAAS,oBAAqB,GAAG;IAC/B,OAAO,SAAS,mBAAmB,MAAM,MAAM;AACjD;AAEA;;;;;;;CAOC,GACD,SAAS,YAAa,KAAK,EAAE,IAAI,EAAE,GAAG;IACpC,MAAM,WAAW,EAAE;IACnB,IAAI;IAEJ,MAAO,CAAC,SAAS,MAAM,IAAI,CAAC,IAAI,MAAM,KAAM;QAC1C,SAAS,IAAI,CAAC;YACZ,MAAM,MAAM,CAAC,EAAE;YACf,OAAO,OAAO,KAAK;YACnB,MAAM;YACN,QAAQ,MAAM,CAAC,EAAE,CAAC,MAAM;QAC1B;IACF;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,sBAAuB,OAAO;IACrC,MAAM,UAAU,YAAY,MAAM,OAAO,EAAE,KAAK,OAAO,EAAE;IACzD,MAAM,eAAe,YAAY,MAAM,YAAY,EAAE,KAAK,YAAY,EAAE;IACxE,IAAI;IACJ,IAAI;IAEJ,IAAI,MAAM,kBAAkB,IAAI;QAC9B,WAAW,YAAY,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE;QAC9C,YAAY,YAAY,MAAM,KAAK,EAAE,KAAK,KAAK,EAAE;IACnD,OAAO;QACL,WAAW,YAAY,MAAM,UAAU,EAAE,KAAK,IAAI,EAAE;QACpD,YAAY,EAAE;IAChB;IAEA,MAAM,OAAO,QAAQ,MAAM,CAAC,cAAc,UAAU;IAEpD,OAAO,KACJ,IAAI,CAAC,SAAU,EAAE,EAAE,EAAE;QACpB,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK;IAC5B,GACC,GAAG,CAAC,SAAU,GAAG;QAChB,OAAO;YACL,MAAM,IAAI,IAAI;YACd,MAAM,IAAI,IAAI;YACd,QAAQ,IAAI,MAAM;QACpB;IACF;AACJ;AAEA;;;;;;;CAOC,GACD,SAAS,qBAAsB,MAAM,EAAE,IAAI;IACzC,OAAQ;QACN,KAAK,KAAK,OAAO;YACf,OAAO,YAAY,aAAa,CAAC;QACnC,KAAK,KAAK,YAAY;YACpB,OAAO,iBAAiB,aAAa,CAAC;QACxC,KAAK,KAAK,KAAK;YACb,OAAO,UAAU,aAAa,CAAC;QACjC,KAAK,KAAK,IAAI;YACZ,OAAO,SAAS,aAAa,CAAC;IAClC;AACF;AAEA;;;;;CAKC,GACD,SAAS,cAAe,IAAI;IAC1B,OAAO,KAAK,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpC,MAAM,UAAU,IAAI,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG;QAC5D,IAAI,WAAW,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YACzC,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,IAAI,IAAI,KAAK,IAAI;YACrC,OAAO;QACT;QAEA,IAAI,IAAI,CAAC;QACT,OAAO;IACT,GAAG,EAAE;AACP;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,WAAY,IAAI;IACvB,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,MAAM,IAAI,CAAC,EAAE;QAEnB,OAAQ,IAAI,IAAI;YACd,KAAK,KAAK,OAAO;gBACf,MAAM,IAAI,CAAC;oBAAC;oBACV;wBAAE,MAAM,IAAI,IAAI;wBAAE,MAAM,KAAK,YAAY;wBAAE,QAAQ,IAAI,MAAM;oBAAC;oBAC9D;wBAAE,MAAM,IAAI,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,QAAQ,IAAI,MAAM;oBAAC;iBACvD;gBACD;YACF,KAAK,KAAK,YAAY;gBACpB,MAAM,IAAI,CAAC;oBAAC;oBACV;wBAAE,MAAM,IAAI,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,QAAQ,IAAI,MAAM;oBAAC;iBACvD;gBACD;YACF,KAAK,KAAK,KAAK;gBACb,MAAM,IAAI,CAAC;oBAAC;oBACV;wBAAE,MAAM,IAAI,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,QAAQ,oBAAoB,IAAI,IAAI;oBAAE;iBAC1E;gBACD;YACF,KAAK,KAAK,IAAI;gBACZ,MAAM,IAAI,CAAC;oBACT;wBAAE,MAAM,IAAI,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,QAAQ,oBAAoB,IAAI,IAAI;oBAAE;iBAC1E;QACL;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,WAAY,KAAK,EAAE,OAAO;IACjC,MAAM,QAAQ,CAAC;IACf,MAAM,QAAQ;QAAE,OAAO,CAAC;IAAE;IAC1B,IAAI,cAAc;QAAC;KAAQ;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,YAAY,KAAK,CAAC,EAAE;QAC1B,MAAM,iBAAiB,EAAE;QAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,MAAM,OAAO,SAAS,CAAC,EAAE;YACzB,MAAM,MAAM,KAAK,IAAI;YAErB,eAAe,IAAI,CAAC;YACpB,KAAK,CAAC,IAAI,GAAG;gBAAE,MAAM;gBAAM,WAAW;YAAE;YACxC,KAAK,CAAC,IAAI,GAAG,CAAC;YAEd,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;gBAC3C,MAAM,aAAa,WAAW,CAAC,EAAE;gBAEjC,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClE,KAAK,CAAC,WAAW,CAAC,IAAI,GACpB,qBAAqB,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,MAAM,EAAE,KAAK,IAAI,IACzE,qBAAqB,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,IAAI;oBAE7D,KAAK,CAAC,WAAW,CAAC,SAAS,IAAI,KAAK,MAAM;gBAC5C,OAAO;oBACL,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,MAAM;oBAEhE,KAAK,CAAC,WAAW,CAAC,IAAI,GAAG,qBAAqB,KAAK,MAAM,EAAE,KAAK,IAAI,IAClE,IAAI,KAAK,qBAAqB,CAAC,KAAK,IAAI,EAAE,UAAS,cAAc;gBACrE;YACF;QACF;QAEA,cAAc;IAChB;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG;IAC9B;IAEA,OAAO;QAAE,KAAK;QAAO,OAAO;IAAM;AACpC;AAEA;;;;;;;CAOC,GACD,SAAS,mBAAoB,IAAI,EAAE,SAAS;IAC1C,IAAI;IACJ,MAAM,WAAW,KAAK,kBAAkB,CAAC;IAEzC,OAAO,KAAK,IAAI,CAAC,WAAW;IAE5B,gCAAgC;IAChC,IAAI,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,SAAS,GAAG,EAAE;QACjD,MAAM,IAAI,MAAM,MAAM,OAAO,MAC3B,kCAAkC,KAAK,QAAQ,CAAC,QAChD,4BAA4B,KAAK,QAAQ,CAAC;IAC9C;IAEA,6CAA6C;IAC7C,IAAI,SAAS,KAAK,KAAK,IAAI,CAAC,MAAM,kBAAkB,IAAI;QACtD,OAAO,KAAK,IAAI;IAClB;IAEA,OAAQ;QACN,KAAK,KAAK,OAAO;YACf,OAAO,IAAI,YAAY;QAEzB,KAAK,KAAK,YAAY;YACpB,OAAO,IAAI,iBAAiB;QAE9B,KAAK,KAAK,KAAK;YACb,OAAO,IAAI,UAAU;QAEvB,KAAK,KAAK,IAAI;YACZ,OAAO,IAAI,SAAS;IACxB;AACF;AAEA;;;;;;;;;;;;;;CAcC,GACD,QAAQ,SAAS,GAAG,SAAS,UAAW,KAAK;IAC3C,OAAO,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;QACpC,IAAI,OAAO,QAAQ,UAAU;YAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK;QACnC,OAAO,IAAI,IAAI,IAAI,EAAE;YACnB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE,IAAI,IAAI;QAChD;QAEA,OAAO;IACT,GAAG,EAAE;AACP;AAEA;;;;;;;CAOC,GACD,QAAQ,UAAU,GAAG,SAAS,WAAY,IAAI,EAAE,OAAO;IACrD,MAAM,OAAO,sBAAsB,MAAM,MAAM,kBAAkB;IAEjE,MAAM,QAAQ,WAAW;IACzB,MAAM,QAAQ,WAAW,OAAO;IAChC,MAAM,OAAO,SAAS,SAAS,CAAC,MAAM,GAAG,EAAE,SAAS;IAEpD,MAAM,gBAAgB,EAAE;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;QACxC,cAAc,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI;IAC9C;IAEA,OAAO,QAAQ,SAAS,CAAC,cAAc;AACzC;AAEA;;;;;;;;;CASC,GACD,QAAQ,QAAQ,GAAG,SAAS,SAAU,IAAI;IACxC,OAAO,QAAQ,SAAS,CACtB,sBAAsB,MAAM,MAAM,kBAAkB;AAExD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/core/qrcode.js"], "sourcesContent": ["const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n"], "names": [], "mappings": "AAAA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,GAEA;;;;;CAKC,GACD,SAAS,mBAAoB,MAAM,EAAE,OAAO;IAC1C,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,MAAM,cAAc,YAAY,CAAC;IAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;QACrB,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;QAErB,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;YAC5B,IAAI,MAAM,KAAK,CAAC,KAAK,QAAQ,MAAM,GAAG;YAEtC,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;gBAC5B,IAAI,MAAM,KAAK,CAAC,KAAK,QAAQ,MAAM,GAAG;gBAEtC,IAAI,AAAC,KAAK,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,KACzC,KAAK,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,KACvC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAI;oBACxC,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM;gBACrC,OAAO;oBACL,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO;gBACtC;YACF;QACF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,mBAAoB,MAAM;IACjC,MAAM,OAAO,OAAO,IAAI;IAExB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,GAAG,IAAK;QACjC,MAAM,QAAQ,IAAI,MAAM;QACxB,OAAO,GAAG,CAAC,GAAG,GAAG,OAAO;QACxB,OAAO,GAAG,CAAC,GAAG,GAAG,OAAO;IAC1B;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,sBAAuB,MAAM,EAAE,OAAO;IAC7C,MAAM,MAAM,iBAAiB,YAAY,CAAC;IAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;QACrB,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE;QAErB,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;YAC5B,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;gBAC5B,IAAI,MAAM,CAAC,KAAK,MAAM,KAAK,MAAM,CAAC,KAAK,MAAM,KAC1C,MAAM,KAAK,MAAM,GAAI;oBACtB,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM;gBACrC,OAAO;oBACL,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO;gBACtC;YACF;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,iBAAkB,MAAM,EAAE,OAAO;IACxC,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,QAAQ,cAAc,CAAC;IACpC,IAAI,KAAK,KAAK;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,MAAM,KAAK,KAAK,CAAC,IAAI;QACrB,MAAM,IAAI,IAAI,OAAO,IAAI;QACzB,MAAM,CAAC,AAAC,QAAQ,IAAK,CAAC,MAAM;QAE5B,OAAO,GAAG,CAAC,KAAK,KAAK,KAAK;QAC1B,OAAO,GAAG,CAAC,KAAK,KAAK,KAAK;IAC5B;AACF;AAEA;;;;;;CAMC,GACD,SAAS,gBAAiB,MAAM,EAAE,oBAAoB,EAAE,WAAW;IACjE,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,WAAW,cAAc,CAAC,sBAAsB;IAC7D,IAAI,GAAG;IAEP,IAAK,IAAI,GAAG,IAAI,IAAI,IAAK;QACvB,MAAM,CAAC,AAAC,QAAQ,IAAK,CAAC,MAAM;QAE5B,WAAW;QACX,IAAI,IAAI,GAAG;YACT,OAAO,GAAG,CAAC,GAAG,GAAG,KAAK;QACxB,OAAO,IAAI,IAAI,GAAG;YAChB,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,KAAK;QAC5B,OAAO;YACL,OAAO,GAAG,CAAC,OAAO,KAAK,GAAG,GAAG,KAAK;QACpC;QAEA,aAAa;QACb,IAAI,IAAI,GAAG;YACT,OAAO,GAAG,CAAC,GAAG,OAAO,IAAI,GAAG,KAAK;QACnC,OAAO,IAAI,IAAI,GAAG;YAChB,OAAO,GAAG,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK;QACrC,OAAO;YACL,OAAO,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK;QACjC;IACF;IAEA,eAAe;IACf,OAAO,GAAG,CAAC,OAAO,GAAG,GAAG,GAAG;AAC7B;AAEA;;;;;CAKC,GACD,SAAS,UAAW,MAAM,EAAE,IAAI;IAC9B,MAAM,OAAO,OAAO,IAAI;IACxB,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,OAAO;IACjB,IAAI,WAAW;IACf,IAAI,YAAY;IAEhB,IAAK,IAAI,MAAM,OAAO,GAAG,MAAM,GAAG,OAAO,EAAG;QAC1C,IAAI,QAAQ,GAAG;QAEf,MAAO,KAAM;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,CAAC,OAAO,UAAU,CAAC,KAAK,MAAM,IAAI;oBACpC,IAAI,OAAO;oBAEX,IAAI,YAAY,KAAK,MAAM,EAAE;wBAC3B,OAAQ,CAAC,AAAC,IAAI,CAAC,UAAU,KAAK,WAAY,CAAC,MAAM;oBACnD;oBAEA,OAAO,GAAG,CAAC,KAAK,MAAM,GAAG;oBACzB;oBAEA,IAAI,aAAa,CAAC,GAAG;wBACnB;wBACA,WAAW;oBACb;gBACF;YACF;YAEA,OAAO;YAEP,IAAI,MAAM,KAAK,QAAQ,KAAK;gBAC1B,OAAO;gBACP,MAAM,CAAC;gBACP;YACF;QACF;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,WAAY,OAAO,EAAE,oBAAoB,EAAE,QAAQ;IAC1D,sBAAsB;IACtB,MAAM,SAAS,IAAI;IAEnB,SAAS,OAAO,CAAC,SAAU,IAAI;QAC7B,2CAA2C;QAC3C,OAAO,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;QAE1B,8CAA8C;QAC9C,wEAAwE;QACxE,+CAA+C;QAC/C,wEAAwE;QACxE,yEAAyE;QACzE,gBAAgB;QAChB,2CAA2C;QAC3C,OAAO,GAAG,CAAC,KAAK,SAAS,IAAI,KAAK,qBAAqB,CAAC,KAAK,IAAI,EAAE;QAEnE,qCAAqC;QACrC,KAAK,KAAK,CAAC;IACb;IAEA,oCAAoC;IACpC,MAAM,iBAAiB,MAAM,uBAAuB,CAAC;IACrD,MAAM,mBAAmB,OAAO,sBAAsB,CAAC,SAAS;IAChE,MAAM,yBAAyB,CAAC,iBAAiB,gBAAgB,IAAI;IAErE,oBAAoB;IACpB,uEAAuE;IACvE,+EAA+E;IAC/E,qFAAqF;IACrF,0BAA0B;IAC1B,IAAI,OAAO,eAAe,KAAK,KAAK,wBAAwB;QAC1D,OAAO,GAAG,CAAC,GAAG;IAChB;IAEA,oFAAoF;IACpF,mDAAmD;IAEnD,2FAA2F;IAC3F,mFAAmF;IACnF,MAAO,OAAO,eAAe,KAAK,MAAM,EAAG;QACzC,OAAO,MAAM,CAAC;IAChB;IAEA,uFAAuF;IACvF,6EAA6E;IAC7E,qFAAqF;IACrF,mCAAmC;IACnC,MAAM,gBAAgB,CAAC,yBAAyB,OAAO,eAAe,EAAE,IAAI;IAC5E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,OAAO,GAAG,CAAC,IAAI,IAAI,OAAO,MAAM;IAClC;IAEA,OAAO,gBAAgB,QAAQ,SAAS;AAC1C;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAiB,SAAS,EAAE,OAAO,EAAE,oBAAoB;IAChE,qEAAqE;IACrE,MAAM,iBAAiB,MAAM,uBAAuB,CAAC;IAErD,6CAA6C;IAC7C,MAAM,mBAAmB,OAAO,sBAAsB,CAAC,SAAS;IAEhE,iCAAiC;IACjC,MAAM,qBAAqB,iBAAiB;IAE5C,yBAAyB;IACzB,MAAM,gBAAgB,OAAO,cAAc,CAAC,SAAS;IAErD,sDAAsD;IACtD,MAAM,iBAAiB,iBAAiB;IACxC,MAAM,iBAAiB,gBAAgB;IAEvC,MAAM,yBAAyB,KAAK,KAAK,CAAC,iBAAiB;IAE3D,MAAM,wBAAwB,KAAK,KAAK,CAAC,qBAAqB;IAC9D,MAAM,wBAAwB,wBAAwB;IAEtD,qDAAqD;IACrD,MAAM,UAAU,yBAAyB;IAEzC,kFAAkF;IAClF,MAAM,KAAK,IAAI,mBAAmB;IAElC,IAAI,SAAS;IACb,MAAM,SAAS,IAAI,MAAM;IACzB,MAAM,SAAS,IAAI,MAAM;IACzB,IAAI,cAAc;IAClB,MAAM,SAAS,IAAI,WAAW,UAAU,MAAM;IAE9C,uDAAuD;IACvD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,MAAM,WAAW,IAAI,iBAAiB,wBAAwB;QAE9D,sCAAsC;QACtC,MAAM,CAAC,EAAE,GAAG,OAAO,KAAK,CAAC,QAAQ,SAAS;QAE1C,6CAA6C;QAC7C,MAAM,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;QAE/B,UAAU;QACV,cAAc,KAAK,GAAG,CAAC,aAAa;IACtC;IAEA,oBAAoB;IACpB,qEAAqE;IACrE,MAAM,OAAO,IAAI,WAAW;IAC5B,IAAI,QAAQ;IACZ,IAAI,GAAG;IAEP,qBAAqB;IACrB,IAAK,IAAI,GAAG,IAAI,aAAa,IAAK;QAChC,IAAK,IAAI,GAAG,IAAI,eAAe,IAAK;YAClC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;gBACxB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE;YAC9B;QACF;IACF;IAEA,qBAAqB;IACrB,IAAK,IAAI,GAAG,IAAI,SAAS,IAAK;QAC5B,IAAK,IAAI,GAAG,IAAI,eAAe,IAAK;YAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE;QAC9B;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAc,IAAI,EAAE,OAAO,EAAE,oBAAoB,EAAE,WAAW;IACrE,IAAI;IAEJ,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,WAAW,SAAS,SAAS,CAAC;IAChC,OAAO,IAAI,OAAO,SAAS,UAAU;QACnC,IAAI,mBAAmB;QAEvB,IAAI,CAAC,kBAAkB;YACrB,MAAM,cAAc,SAAS,QAAQ,CAAC;YAEtC,+DAA+D;YAC/D,mBAAmB,QAAQ,qBAAqB,CAAC,aAAa;QAChE;QAEA,2BAA2B;QAC3B,kEAAkE;QAClE,WAAW,SAAS,UAAU,CAAC,MAAM,oBAAoB;IAC3D,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,cAAc,QAAQ,qBAAqB,CAAC,UAAU;IAE5D,gDAAgD;IAChD,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,+CAA+C;IAC/C,IAAI,CAAC,SAAS;QACZ,UAAU;IAEZ,sDAAsD;IACtD,OAAO,IAAI,UAAU,aAAa;QAChC,MAAM,IAAI,MAAM,OACd,qEACA,wDAAwD,cAAc;IAE1E;IAEA,MAAM,WAAW,WAAW,SAAS,sBAAsB;IAE3D,yBAAyB;IACzB,MAAM,cAAc,MAAM,aAAa,CAAC;IACxC,MAAM,UAAU,IAAI,UAAU;IAE9B,uBAAuB;IACvB,mBAAmB,SAAS;IAC5B,mBAAmB;IACnB,sBAAsB,SAAS;IAE/B,yEAAyE;IACzE,0FAA0F;IAC1F,6EAA6E;IAC7E,mEAAmE;IACnE,gBAAgB,SAAS,sBAAsB;IAE/C,IAAI,WAAW,GAAG;QAChB,iBAAiB,SAAS;IAC5B;IAEA,qBAAqB;IACrB,UAAU,SAAS;IAEnB,IAAI,MAAM,cAAc;QACtB,yBAAyB;QACzB,cAAc,YAAY,WAAW,CAAC,SACpC,gBAAgB,IAAI,CAAC,MAAM,SAAS;IACxC;IAEA,qBAAqB;IACrB,YAAY,SAAS,CAAC,aAAa;IAEnC,+CAA+C;IAC/C,gBAAgB,SAAS,sBAAsB;IAE/C,OAAO;QACL,SAAS;QACT,SAAS;QACT,sBAAsB;QACtB,aAAa;QACb,UAAU;IACZ;AACF;AAEA;;;;;;;;CAQC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAQ,IAAI,EAAE,OAAO;IAC7C,IAAI,OAAO,SAAS,eAAe,SAAS,IAAI;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,uBAAuB,QAAQ,CAAC;IACpC,IAAI;IACJ,IAAI;IAEJ,IAAI,OAAO,YAAY,aAAa;QAClC,+CAA+C;QAC/C,uBAAuB,QAAQ,IAAI,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,CAAC;QAC3E,UAAU,QAAQ,IAAI,CAAC,QAAQ,OAAO;QACtC,OAAO,YAAY,IAAI,CAAC,QAAQ,WAAW;QAE3C,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,iBAAiB,CAAC,QAAQ,UAAU;QAC5C;IACF;IAEA,OAAO,aAAa,MAAM,SAAS,sBAAsB;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/renderer/utils.js"], "sourcesContent": ["function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,SAAS,SAAU,GAAG;IACpB,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,QAAQ;IACpB;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,UAAU,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;IACjD,IAAI,QAAQ,MAAM,GAAG,KAAK,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,GAAG,GAAG;QACpE,MAAM,IAAI,MAAM,wBAAwB;IAC1C;IAEA,kDAAkD;IAClD,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;QAChD,UAAU,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,CAAC,SAAU,CAAC;YAChE,OAAO;gBAAC;gBAAG;aAAE;QACf;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK;IAE5C,MAAM,WAAW,SAAS,QAAQ,IAAI,CAAC,KAAK;IAE5C,OAAO;QACL,GAAG,AAAC,YAAY,KAAM;QACtB,GAAG,AAAC,YAAY,KAAM;QACtB,GAAG,AAAC,YAAY,IAAK;QACrB,GAAG,WAAW;QACd,KAAK,MAAM,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IACtC;AACF;AAEA,QAAQ,UAAU,GAAG,SAAS,WAAY,OAAO;IAC/C,IAAI,CAAC,SAAS,UAAU,CAAC;IACzB,IAAI,CAAC,QAAQ,KAAK,EAAE,QAAQ,KAAK,GAAG,CAAC;IAErC,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK,eACvC,QAAQ,MAAM,KAAK,QACnB,QAAQ,MAAM,GAAG,IACf,IACA,QAAQ,MAAM;IAElB,MAAM,QAAQ,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,GAAG;IACrE,MAAM,QAAQ,QAAQ,KAAK,IAAI;IAE/B,OAAO;QACL,OAAO;QACP,OAAO,QAAQ,IAAI;QACnB,QAAQ;QACR,OAAO;YACL,MAAM,SAAS,QAAQ,KAAK,CAAC,IAAI,IAAI;YACrC,OAAO,SAAS,QAAQ,KAAK,CAAC,KAAK,IAAI;QACzC;QACA,MAAM,QAAQ,IAAI;QAClB,cAAc,QAAQ,YAAY,IAAI,CAAC;IACzC;AACF;AAEA,QAAQ,QAAQ,GAAG,SAAS,SAAU,MAAM,EAAE,IAAI;IAChD,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,IACtD,KAAK,KAAK,GAAG,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,IACtC,KAAK,KAAK;AAChB;AAEA,QAAQ,aAAa,GAAG,SAAS,cAAe,MAAM,EAAE,IAAI;IAC1D,MAAM,QAAQ,QAAQ,QAAQ,CAAC,QAAQ;IACvC,OAAO,KAAK,KAAK,CAAC,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,IAAI;AACjD;AAEA,QAAQ,aAAa,GAAG,SAAS,cAAe,OAAO,EAAE,EAAE,EAAE,IAAI;IAC/D,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI;IAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI;IAC5B,MAAM,QAAQ,QAAQ,QAAQ,CAAC,MAAM;IACrC,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,MAAM,GAAG,CAAC,IAAI;IACzD,MAAM,eAAe,KAAK,MAAM,GAAG;IACnC,MAAM,UAAU;QAAC,KAAK,KAAK,CAAC,KAAK;QAAE,KAAK,KAAK,CAAC,IAAI;KAAC;IAEnD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,IAAI,SAAS,CAAC,IAAI,aAAa,CAAC,IAAI;YACpC,IAAI,UAAU,KAAK,KAAK,CAAC,KAAK;YAE9B,IAAI,KAAK,gBAAgB,KAAK,gBAC5B,IAAI,aAAa,gBAAgB,IAAI,aAAa,cAAc;gBAChE,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,YAAY,IAAI;gBAC7C,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,YAAY,IAAI;gBAC7C,UAAU,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,KAAK,GAAG,IAAI,EAAE;YACrD;YAEA,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC7B,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC7B,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC7B,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC;QAC7B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2593, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/renderer/canvas.js"], "sourcesContent": ["const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,YAAa,GAAG,EAAE,MAAM,EAAE,IAAI;IACrC,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;IAE/C,IAAI,CAAC,OAAO,KAAK,EAAE,OAAO,KAAK,GAAG,CAAC;IACnC,OAAO,MAAM,GAAG;IAChB,OAAO,KAAK,GAAG;IACf,OAAO,KAAK,CAAC,MAAM,GAAG,OAAO;IAC7B,OAAO,KAAK,CAAC,KAAK,GAAG,OAAO;AAC9B;AAEA,SAAS;IACP,IAAI;QACF,OAAO,SAAS,aAAa,CAAC;IAChC,EAAE,OAAO,GAAG;QACV,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,QAAQ,MAAM,GAAG,SAAS,OAAQ,MAAM,EAAE,MAAM,EAAE,OAAO;IACvD,IAAI,OAAO;IACX,IAAI,WAAW;IAEf,IAAI,OAAO,SAAS,eAAe,CAAC,CAAC,UAAU,CAAC,OAAO,UAAU,GAAG;QAClE,OAAO;QACP,SAAS;IACX;IAEA,IAAI,CAAC,QAAQ;QACX,WAAW;IACb;IAEA,OAAO,MAAM,UAAU,CAAC;IACxB,MAAM,OAAO,MAAM,aAAa,CAAC,OAAO,OAAO,CAAC,IAAI,EAAE;IAEtD,MAAM,MAAM,SAAS,UAAU,CAAC;IAChC,MAAM,QAAQ,IAAI,eAAe,CAAC,MAAM;IACxC,MAAM,aAAa,CAAC,MAAM,IAAI,EAAE,QAAQ;IAExC,YAAY,KAAK,UAAU;IAC3B,IAAI,YAAY,CAAC,OAAO,GAAG;IAE3B,OAAO;AACT;AAEA,QAAQ,eAAe,GAAG,SAAS,gBAAiB,MAAM,EAAE,MAAM,EAAE,OAAO;IACzE,IAAI,OAAO;IAEX,IAAI,OAAO,SAAS,eAAe,CAAC,CAAC,UAAU,CAAC,OAAO,UAAU,GAAG;QAClE,OAAO;QACP,SAAS;IACX;IAEA,IAAI,CAAC,MAAM,OAAO,CAAC;IAEnB,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,QAAQ;IAEhD,MAAM,OAAO,KAAK,IAAI,IAAI;IAC1B,MAAM,eAAe,KAAK,YAAY,IAAI,CAAC;IAE3C,OAAO,SAAS,SAAS,CAAC,MAAM,aAAa,OAAO;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/renderer/svg-tag.js"], "sourcesContent": ["const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n"], "names": [], "mappings": "AAAA,MAAM;AAEN,SAAS,eAAgB,KAAK,EAAE,MAAM;IACpC,MAAM,QAAQ,MAAM,CAAC,GAAG;IACxB,MAAM,MAAM,SAAS,OAAO,MAAM,GAAG,GAAG;IAExC,OAAO,QAAQ,IACX,MAAM,MAAM,SAAS,eAAe,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,MAChE;AACN;AAEA,SAAS,OAAQ,GAAG,EAAE,CAAC,EAAE,CAAC;IACxB,IAAI,MAAM,MAAM;IAChB,IAAI,OAAO,MAAM,aAAa,OAAO,MAAM;IAE3C,OAAO;AACT;AAEA,SAAS,SAAU,IAAI,EAAE,IAAI,EAAE,MAAM;IACnC,IAAI,OAAO;IACX,IAAI,SAAS;IACb,IAAI,SAAS;IACb,IAAI,aAAa;IAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;QAC3B,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;QAE3B,IAAI,CAAC,OAAO,CAAC,QAAQ,SAAS;QAE9B,IAAI,IAAI,CAAC,EAAE,EAAE;YACX;YAEA,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG;gBACtC,QAAQ,SACJ,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,UACtC,OAAO,KAAK,QAAQ;gBAExB,SAAS;gBACT,SAAS;YACX;YAEA,IAAI,CAAC,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG;gBACpC,QAAQ,OAAO,KAAK;gBACpB,aAAa;YACf;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT;AAEA,QAAQ,MAAM,GAAG,SAAS,OAAQ,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,MAAM,OAAO,MAAM,UAAU,CAAC;IAC9B,MAAM,OAAO,OAAO,OAAO,CAAC,IAAI;IAChC,MAAM,OAAO,OAAO,OAAO,CAAC,IAAI;IAChC,MAAM,aAAa,OAAO,KAAK,MAAM,GAAG;IAExC,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,GAC1B,KACA,WAAW,eAAe,KAAK,KAAK,CAAC,KAAK,EAAE,UAC5C,cAAc,aAAa,MAAM,aAAa;IAElD,MAAM,OACJ,WAAW,eAAe,KAAK,KAAK,CAAC,IAAI,EAAE,YAC3C,SAAS,SAAS,MAAM,MAAM,KAAK,MAAM,IAAI;IAE/C,MAAM,UAAU,cAAc,SAAS,aAAa,MAAM,aAAa;IAEvE,MAAM,QAAQ,CAAC,KAAK,KAAK,GAAG,KAAK,YAAY,KAAK,KAAK,GAAG,eAAe,KAAK,KAAK,GAAG;IAEtF,MAAM,SAAS,6CAA6C,QAAQ,UAAU,mCAAmC,KAAK,OAAO;IAE7H,IAAI,OAAO,OAAO,YAAY;QAC5B,GAAG,MAAM;IACX;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2702, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/qrcode/lib/browser.js"], "sourcesContent": ["\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n"], "names": [], "mappings": "AACA,MAAM;AAEN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,SAAS,aAAc,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;IACvD,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACtC,MAAM,UAAU,KAAK,MAAM;IAC3B,MAAM,cAAc,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK;IAEjD,IAAI,CAAC,eAAe,CAAC,cAAc;QACjC,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,aAAa;QACf,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,YAAY,GAAG;YACjB,KAAK;YACL,OAAO;YACP,SAAS,OAAO;QAClB,OAAO,IAAI,YAAY,GAAG;YACxB,IAAI,OAAO,UAAU,IAAI,OAAO,OAAO,aAAa;gBAClD,KAAK;gBACL,OAAO;YACT,OAAO;gBACL,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;IACF,OAAO;QACL,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,YAAY,GAAG;YACjB,OAAO;YACP,SAAS,OAAO;QAClB,OAAO,IAAI,YAAY,KAAK,CAAC,OAAO,UAAU,EAAE;YAC9C,OAAO;YACP,OAAO;YACP,SAAS;QACX;QAEA,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,IAAI;gBACF,MAAM,OAAO,OAAO,MAAM,CAAC,MAAM;gBACjC,QAAQ,WAAW,MAAM,QAAQ;YACnC,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;IACF;IAEA,IAAI;QACF,MAAM,OAAO,OAAO,MAAM,CAAC,MAAM;QACjC,GAAG,MAAM,WAAW,MAAM,QAAQ;IACpC,EAAE,OAAO,GAAG;QACV,GAAG;IACL;AACF;AAEA,QAAQ,MAAM,GAAG,OAAO,MAAM;AAC9B,QAAQ,QAAQ,GAAG,aAAa,IAAI,CAAC,MAAM,eAAe,MAAM;AAChE,QAAQ,SAAS,GAAG,aAAa,IAAI,CAAC,MAAM,eAAe,eAAe;AAE1E,oBAAoB;AACpB,QAAQ,QAAQ,GAAG,aAAa,IAAI,CAAC,MAAM,SAAU,IAAI,EAAE,CAAC,EAAE,IAAI;IAChE,OAAO,YAAY,MAAM,CAAC,MAAM;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2773, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/aug-pay/node_modules/dijkstrajs/dijkstra.js"], "sourcesContent": ["'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function(graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n\n    var closest,\n        u, v,\n        cost_of_s_to_u,\n        adjacent_nodes,\n        cost_of_e,\n        cost_of_s_to_u_plus_cost_of_e,\n        cost_of_s_to_v,\n        first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = (typeof costs[v] === 'undefined');\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n\n    return predecessors;\n  },\n\n  extract_shortest_path_from_predecessor_list: function(predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n\n  find_path: function(graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(\n      predecessors, d);\n  },\n\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n          t = {},\n          key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {value: value, cost: cost};\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;6EAoB6E,GAC7E,IAAI,WAAW;IACb,8BAA8B,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;QAChD,2DAA2D;QAC3D,iCAAiC;QACjC,IAAI,eAAe,CAAC;QAEpB,2DAA2D;QAC3D,kBAAkB;QAClB,IAAI,QAAQ,CAAC;QACb,KAAK,CAAC,EAAE,GAAG;QAEX,wEAAwE;QACxE,yEAAyE;QACzE,kCAAkC;QAClC,oDAAoD;QACpD,IAAI,OAAO,SAAS,aAAa,CAAC,IAAI;QACtC,KAAK,IAAI,CAAC,GAAG;QAEb,IAAI,SACA,GAAG,GACH,gBACA,gBACA,WACA,+BACA,gBACA;QACJ,MAAO,CAAC,KAAK,KAAK,GAAI;YACpB,iEAAiE;YACjE,iEAAiE;YACjE,UAAU,KAAK,GAAG;YAClB,IAAI,QAAQ,KAAK;YACjB,iBAAiB,QAAQ,IAAI;YAE7B,6BAA6B;YAC7B,iBAAiB,KAAK,CAAC,EAAE,IAAI,CAAC;YAE9B,mEAAmE;YACnE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAK,KAAK,eAAgB;gBACxB,IAAI,eAAe,cAAc,CAAC,IAAI;oBACpC,gDAAgD;oBAChD,YAAY,cAAc,CAAC,EAAE;oBAE7B,+DAA+D;oBAC/D,gEAAgE;oBAChE,mBAAmB;oBACnB,gCAAgC,iBAAiB;oBAEjD,qEAAqE;oBACrE,oEAAoE;oBACpE,iEAAiE;oBACjE,+DAA+D;oBAC/D,iBAAiB,KAAK,CAAC,EAAE;oBACzB,cAAe,OAAO,KAAK,CAAC,EAAE,KAAK;oBACnC,IAAI,eAAe,iBAAiB,+BAA+B;wBACjE,KAAK,CAAC,EAAE,GAAG;wBACX,KAAK,IAAI,CAAC,GAAG;wBACb,YAAY,CAAC,EAAE,GAAG;oBACpB;gBACF;YACF;QACF;QAEA,IAAI,OAAO,MAAM,eAAe,OAAO,KAAK,CAAC,EAAE,KAAK,aAAa;YAC/D,IAAI,MAAM;gBAAC;gBAA+B;gBAAG;gBAAQ;gBAAG;aAAI,CAAC,IAAI,CAAC;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA,6CAA6C,SAAS,YAAY,EAAE,CAAC;QACnE,IAAI,QAAQ,EAAE;QACd,IAAI,IAAI;QACR,IAAI;QACJ,MAAO,EAAG;YACR,MAAM,IAAI,CAAC;YACX,cAAc,YAAY,CAAC,EAAE;YAC7B,IAAI,YAAY,CAAC,EAAE;QACrB;QACA,MAAM,OAAO;QACb,OAAO;IACT;IAEA,WAAW,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;QAC7B,IAAI,eAAe,SAAS,4BAA4B,CAAC,OAAO,GAAG;QACnE,OAAO,SAAS,2CAA2C,CACzD,cAAc;IAClB;IAEA;;GAEC,GACD,eAAe;QACb,MAAM,SAAU,IAAI;YAClB,IAAI,IAAI,SAAS,aAAa,EAC1B,IAAI,CAAC,GACL;YACJ,OAAO,QAAQ,CAAC;YAChB,IAAK,OAAO,EAAG;gBACb,IAAI,EAAE,cAAc,CAAC,MAAM;oBACzB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;gBACjB;YACF;YACA,EAAE,KAAK,GAAG,EAAE;YACZ,EAAE,MAAM,GAAG,KAAK,MAAM,IAAI,EAAE,cAAc;YAC1C,OAAO;QACT;QAEA,gBAAgB,SAAU,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI;QACxB;QAEA;;;KAGC,GACD,MAAM,SAAU,KAAK,EAAE,IAAI;YACzB,IAAI,OAAO;gBAAC,OAAO;gBAAO,MAAM;YAAI;YACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAC7B;QAEA;;KAEC,GACD,KAAK;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;QACzB;QAEA,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK;QAC/B;IACF;AACF;AAGA,yBAAyB;AACzB,wCAAmC;IACjC,OAAO,OAAO,GAAG;AACnB", "ignoreList": [0], "debugId": null}}]}