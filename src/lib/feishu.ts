import { Client } from '@larksuiteoapi/node-sdk';

// 飞书客户端配置
const client = new Client({
  appId: process.env.FEISHU_APP_ID!,
  appSecret: process.env.FEISHU_APP_SECRET!,
  disableTokenCache: false, // 启用token缓存，SDK自动管理token
});

// 飞书表格配置
const FEISHU_CONFIG = {
  appToken: process.env.FEISHU_APP_TOKEN!,
  tableId: process.env.FEISHU_TABLE_ID!,
};

// 账号状态统计接口
export interface AccountStats {
  total: number;      // 总账号数
  sold: number;       // 已售账号数
  available: number;  // 可用账号数
  soldRate: string;   // 销售率（百分比）
}

// 账号记录接口
export interface AccountRecord {
  recordId: string;
  account: string;
  registered: boolean;
  promotion: boolean;
  sold: boolean;
  registrationDate?: number;
  email?: string;
}

/**
 * 获取账号统计信息
 * @returns Promise<AccountStats> 账号统计数据
 */
export async function getAccountStats(): Promise<AccountStats> {
  try {
    let allRecords: any[] = [];
    let pageToken: string | undefined;
    let hasMore = true;

    // 分页获取所有记录
    while (hasMore) {
      const response = await client.bitable.v1.appTableRecord.search({
        path: {
          app_token: FEISHU_CONFIG.appToken,
          table_id: FEISHU_CONFIG.tableId,
        },
        params: {
          page_size: 500, // 每页最大500条
          page_token: pageToken,
        },
        data: {
          automatic_fields: false, // 不需要自动字段
        },
      });

      if (response.code === 0 && response.data) {
        allRecords = allRecords.concat(response.data.items || []);
        hasMore = response.data.has_more || false;
        pageToken = response.data.page_token;
      } else {
        throw new Error(`飞书API调用失败: ${response.msg}`);
      }
    }

    // 统计数据
    const total = allRecords.length;
    const sold = allRecords.filter(record => record.fields?.已售 === true).length;
    const available = total - sold;
    const soldRate = total > 0 ? ((sold / total) * 100).toFixed(1) : '0.0';

    return {
      total,
      sold,
      available,
      soldRate: `${soldRate}%`,
    };
  } catch (error) {
    console.error('获取账号统计失败:', error);
    throw new Error('获取账号统计失败');
  }
}

/**
 * 获取可用账号列表
 * @param limit 限制返回数量，默认10
 * @returns Promise<AccountRecord[]> 可用账号列表
 */
export async function getAvailableAccounts(limit: number = 10): Promise<AccountRecord[]> {
  try {
    const response = await client.bitable.v1.appTableRecord.search({
      path: {
        app_token: FEISHU_CONFIG.appToken,
        table_id: FEISHU_CONFIG.tableId,
      },
      params: {
        page_size: Math.min(limit * 2, 500), // 获取更多数据以确保有足够的可用账号
      },
      data: {
        filter: {
          conjunction: 'and',
          conditions: [
            {
              field_name: '已售',
              operator: 'is',
              value: ['false'], // 查询未售账号
            },
          ],
        },
        automatic_fields: false,
      },
    });

    if (response.code === 0 && response.data?.items) {
      return response.data.items
        .filter(record => record.fields?.已售 !== true) // 双重过滤确保未售
        .slice(0, limit) // 限制返回数量
        .map(record => ({
          recordId: record.record_id,
          account: record.fields?.账号?.[0]?.text || '',
          registered: record.fields?.注册 === true,
          promotion: record.fields?.促销 === true,
          sold: record.fields?.已售 === true,
          registrationDate: record.fields?.注册日期,
          email: record.fields?.email?.[0]?.text || record.fields?.email?.[0]?.link?.replace('mailto:', ''),
        }));
    } else {
      throw new Error(`飞书API调用失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('获取可用账号失败:', error);
    throw new Error('获取可用账号失败');
  }
}

/**
 * 标记账号为已售
 * @param recordId 记录ID
 * @param buyerEmail 购买者邮箱
 * @returns Promise<boolean> 是否成功
 */
export async function markAccountAsSold(recordId: string, buyerEmail: string): Promise<boolean> {
  try {
    const response = await client.bitable.v1.appTableRecord.update({
      path: {
        app_token: FEISHU_CONFIG.appToken,
        table_id: FEISHU_CONFIG.tableId,
        record_id: recordId,
      },
      data: {
        fields: {
          已售: true,
          email: [{ text: buyerEmail, type: 'text' }],
        },
      },
    });

    if (response.code === 0) {
      console.log(`账号已标记为已售: ${recordId}, 购买者: ${buyerEmail}`);
      return true;
    } else {
      console.error(`标记账号失败: ${response.msg}`);
      return false;
    }
  } catch (error) {
    console.error('标记账号为已售失败:', error);
    return false;
  }
}

/**
 * 测试飞书连接
 * @returns Promise<boolean> 连接是否成功
 */
export async function testFeishuConnection(): Promise<boolean> {
  try {
    const response = await client.bitable.v1.appTable.list({
      path: {
        app_token: FEISHU_CONFIG.appToken,
      },
      params: {
        page_size: 1,
      },
    });

    return response.code === 0;
  } catch (error) {
    console.error('飞书连接测试失败:', error);
    return false;
  }
}
