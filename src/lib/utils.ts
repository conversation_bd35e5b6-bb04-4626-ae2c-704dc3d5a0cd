import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

// 生成唯一订单号
export function generateOrderId(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8);
  return `ORDER_${timestamp}_${random}`;
}

// 生成唯一支付ID
export function generatePaymentId(): string {
  return `PAY_${uuidv4().replace(/-/g, '').substring(0, 16)}`;
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 格式化金额（分转元）
export function formatAmount(amount: number): string {
  return (amount / 100).toFixed(2);
}

// 金额转换（元转分）
export function amountToFen(amount: number): number {
  return Math.round(amount * 100);
}

// 生成微信支付签名
export function generateWechatSignature(
  params: Record<string, any>,
  apiKey: string
): string {
  // 按字典序排序参数
  const sortedKeys = Object.keys(params).sort();
  const stringA = sortedKeys
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const stringSignTemp = `${stringA}&key=${apiKey}`;
  return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
}

// 验证微信支付回调签名
export function verifyWechatSignature(
  params: Record<string, any>,
  signature: string,
  apiKey: string
): boolean {
  const { sign, ...otherParams } = params;
  const calculatedSignature = generateWechatSignature(otherParams, apiKey);
  return calculatedSignature === signature;
}

// 生成随机字符串
export function generateNonceStr(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 延迟函数
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 错误处理包装器
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<{ success: boolean; data?: R; error?: string }> => {
    try {
      const data = await fn(...args);
      return { success: true, data };
    } catch (error) {
      console.error('Error in function:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  };
}
