'use client';

import { useState, useEffect } from 'react';
import PurchaseForm from '@/components/PurchaseForm';

export default function Home() {
  const [priceDisplay, setPriceDisplay] = useState('¥99');

  useEffect(() => {
    // 获取价格配置
    const fetchPrice = async () => {
      try {
        const response = await fetch('/api/config/price');
        const data = await response.json();
        if (data.success) {
          setPriceDisplay(data.data.priceDisplay);
        }
      } catch (error) {
        console.error('获取价格配置失败:', error);
      }
    };

    fetchPrice();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* 头部 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Augment 账号
            <span className="text-blue-600">自动售卖</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            快速获取 Augment 账号，提升您的开发效率。
            填写邮箱，微信支付，即刻获得专属账号。
          </p>
        </div>

        {/* 特性介绍 */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">即时交付</h3>
            <p className="text-gray-600">支付成功后立即获得 Augment 账号，无需等待</p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">安全可靠</h3>
            <p className="text-gray-600">微信支付保障，交易安全有保证</p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">24/7 服务</h3>
            <p className="text-gray-600">全天候自动化服务，随时购买随时使用</p>
          </div>
        </div>

        {/* 购买表单 */}
        <div className="max-w-md mx-auto">
          <PurchaseForm />
        </div>

        {/* 价格说明 */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-lg mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">价格透明</h3>
            <div className="text-4xl font-bold text-blue-600 mb-2">{priceDisplay}</div>
            <p className="text-gray-600">一次性购买，永久使用</p>
          </div>
        </div>
      </div>
    </div>
  );
}
