import { NextRequest, NextResponse } from 'next/server';
import { OrderService, PaymentService } from '@/lib/database';
import { parseV3WechatNotification, generateV3WechatNotificationResponse } from '@/lib/wechat-pay';

export async function POST(request: NextRequest) {
  try {
    // 获取原始请求体
    const body = await request.text();

    // 获取微信支付 API v3 回调头部信息
    const signature = request.headers.get('Wechatpay-Signature') || '';
    const timestamp = request.headers.get('Wechatpay-Timestamp') || '';
    const nonce = request.headers.get('Wechatpay-Nonce') || '';
    const serialNumber = request.headers.get('Wechatpay-Serial') || '';

    console.log('收到微信支付 API v3 回调:', {
      body: body.substring(0, 200) + '...',
      signature: signature.substring(0, 50) + '...',
      timestamp,
      nonce,
      serialNumber
    });

    // 解析微信支付 API v3 通知
    const parseResult = parseV3WechatNotification(body, signature, timestamp, nonce, serialNumber);

    if (!parseResult.success) {
      console.error('微信支付回调验证失败:', parseResult.error);
      return NextResponse.json(
        generateV3WechatNotificationResponse(false, parseResult.error),
        { status: 400 }
      );
    }

    const notificationData = parseResult.data!;

    // 检查支付状态 (API v3 使用 trade_state)
    if (notificationData.trade_state !== 'SUCCESS') {
      console.error('微信支付状态非成功:', notificationData.trade_state_desc);

      // 更新订单状态为失败
      const orderId = notificationData.out_trade_no;
      if (orderId) {
        await OrderService.updateOrderStatus(orderId, 'FAILED');

        // 更新支付记录状态
        const payment = await PaymentService.getPaymentByOrderId(orderId);
        if (payment) {
          await PaymentService.updatePaymentStatus(payment.id, 'FAILED');
        }
      }

      return NextResponse.json(
        generateV3WechatNotificationResponse(true, 'OK'),
        { status: 200 }
      );
    }

    // 获取订单信息 (API v3 格式)
    const orderId = notificationData.out_trade_no;
    const wechatOrderId = notificationData.transaction_id;
    const totalFee = notificationData.amount.total;

    if (!orderId) {
      console.error('微信支付回调缺少订单号');
      return NextResponse.json(
        generateV3WechatNotificationResponse(false, '订单号缺失'),
        { status: 400 }
      );
    }

    // 查询订单
    const order = await OrderService.getOrderById(orderId);

    if (!order) {
      console.error('订单不存在:', orderId);
      return NextResponse.json(
        generateV3WechatNotificationResponse(false, '订单不存在'),
        { status: 400 }
      );
    }

    // 验证金额
    if (order.amount !== totalFee) {
      console.error('订单金额不匹配:', {
        orderAmount: order.amount,
        paidAmount: totalFee
      });
      return NextResponse.json(
        generateV3WechatNotificationResponse(false, '金额不匹配'),
        { status: 400 }
      );
    }

    // 检查订单是否已处理
    if (order.status === 'COMPLETED') {
      console.log('订单已处理完成:', orderId);
      return NextResponse.json(
        generateV3WechatNotificationResponse(true, 'OK'),
        { status: 200 }
      );
    }

    try {
      // 更新支付记录状态
      const payment = await PaymentService.getPaymentByOrderId(orderId);
      if (payment) {
        await PaymentService.updatePaymentStatus(
          payment.id, 
          'SUCCESS', 
          wechatOrderId
        );
      }

      // 更新订单状态为已支付
      await OrderService.updateOrderStatus(orderId, 'PAID');

      // 分配 Augment 账号
      // 这里应该从账号池中分配一个可用账号
      // 由于账号池功能后续开发，这里先模拟分配
      const mockAugmentEmail = `augment_${Date.now()}@example.com`;
      await OrderService.assignAugmentAccount(orderId, mockAugmentEmail);

      console.log('订单处理成功:', {
        orderId,
        wechatOrderId,
        augmentEmail: mockAugmentEmail
      });

      // TODO: 发送邮件通知用户
      // await sendEmailNotification(order.userEmail, mockAugmentEmail);

    } catch (error) {
      console.error('处理支付回调失败:', error);
      return NextResponse.json(
        generateV3WechatNotificationResponse(false, '处理失败'),
        { status: 500 }
      );
    }

    // 返回成功响应
    return NextResponse.json(
      generateV3WechatNotificationResponse(true, 'OK'),
      { status: 200 }
    );

  } catch (error) {
    console.error('微信支付回调处理异常:', error);

    return NextResponse.json(
      generateV3WechatNotificationResponse(false, '服务器错误'),
      { status: 500 }
    );
  }
}
