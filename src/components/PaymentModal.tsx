'use client';

import { useState, useEffect } from 'react';
import QRCode from 'qrcode';

interface PaymentModalProps {
  orderId: string;
  qrCodeUrl: string;
  amount: number;
  onSuccess: (augmentEmail: string) => void;
  onClose: () => void;
}

export default function PaymentModal({
  orderId,
  qrCodeUrl,
  amount,
  onSuccess,
  onClose
}: PaymentModalProps) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState('');
  const [isPolling, setIsPolling] = useState(true);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15分钟倒计时

  // 生成二维码
  useEffect(() => {
    const generateQRCode = async () => {
      try {
        const dataUrl = await QRCode.toDataURL(qrCodeUrl, {
          width: 256,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        setQrCodeDataUrl(dataUrl);
      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    };

    if (qrCodeUrl) {
      generateQRCode();
    }
  }, [qrCodeUrl]);

  // 轮询支付状态
  useEffect(() => {
    if (!isPolling) return;

    const pollPaymentStatus = async () => {
      try {
        const response = await fetch(`/api/orders/status?orderId=${orderId}`);
        const data = await response.json();

        if (data.success && data.data.status === 'COMPLETED' && data.data.augmentEmail) {
          setIsPolling(false);
          onSuccess(data.data.augmentEmail);
        }
      } catch (error) {
        console.error('查询支付状态失败:', error);
      }
    };

    const interval = setInterval(pollPaymentStatus, 3000); // 每3秒查询一次

    return () => clearInterval(interval);
  }, [orderId, isPolling, onSuccess]);

  // 倒计时
  useEffect(() => {
    if (timeLeft <= 0) {
      setIsPolling(false);
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleClose = () => {
    setIsPolling(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 标题 */}
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-gray-900 mb-2">微信支付</h3>
          <p className="text-gray-600">请使用微信扫描下方二维码完成支付</p>
        </div>

        {/* 订单信息 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">商品</span>
            <span className="font-medium">Augment 账号</span>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">订单号</span>
            <span className="font-mono text-sm">{orderId}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">金额</span>
            <span className="font-bold text-lg text-blue-600">¥{amount}</span>
          </div>
        </div>

        {/* 二维码 */}
        <div className="text-center mb-6">
          {qrCodeDataUrl ? (
            <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
              <img
                src={qrCodeDataUrl}
                alt="支付二维码"
                className="w-48 h-48 mx-auto"
              />
            </div>
          ) : (
            <div className="w-48 h-48 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          )}
        </div>

        {/* 倒计时和状态 */}
        <div className="text-center mb-4">
          {timeLeft > 0 ? (
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span className="text-orange-600 font-medium">
                支付剩余时间: {formatTime(timeLeft)}
              </span>
            </div>
          ) : (
            <div className="text-red-600 font-medium">
              支付已超时，请重新创建订单
            </div>
          )}
        </div>

        {/* 支付状态 */}
        {isPolling && timeLeft > 0 && (
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>等待支付中...</span>
            </div>
          </div>
        )}

        {/* 支付说明 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            支付成功后，系统将自动为您分配 Augment 账号
          </p>
        </div>
      </div>
    </div>
  );
}
