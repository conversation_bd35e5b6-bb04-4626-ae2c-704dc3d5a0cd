'use client';

import { useState, useEffect } from 'react';
import { isValidEmail } from '@/lib/utils';
import PaymentModal from './PaymentModal';

export default function PurchaseForm() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [orderId, setOrderId] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [error, setError] = useState('');
  const [price, setPrice] = useState(99);
  const [priceDisplay, setPriceDisplay] = useState('¥99');

  useEffect(() => {
    // 获取价格配置
    const fetchPrice = async () => {
      try {
        const response = await fetch('/api/config/price');
        const data = await response.json();
        if (data.success) {
          setPrice(data.data.price);
          setPriceDisplay(data.data.priceDisplay);
        }
      } catch (error) {
        console.error('获取价格配置失败:', error);
      }
    };

    fetchPrice();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // 验证邮箱
    if (!email.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    if (!isValidEmail(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    setIsLoading(true);

    try {
      // 创建订单
      const response = await fetch('/api/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userEmail: email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '创建订单失败');
      }

      // 显示支付模态框
      setOrderId(data.data.orderId);
      setQrCodeUrl(data.data.qrCodeUrl);
      setShowPayment(true);
    } catch (error) {
      console.error('创建订单失败:', error);
      setError(error instanceof Error ? error.message : '创建订单失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = (augmentEmail: string) => {
    setShowPayment(false);
    // 这里可以显示成功页面或重定向
    alert(`支付成功！您的 Augment 账号：${augmentEmail}`);
    setEmail('');
    setOrderId('');
    setQrCodeUrl('');
  };

  const handlePaymentClose = () => {
    setShowPayment(false);
    setOrderId('');
    setQrCodeUrl('');
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          立即购买 Augment 账号
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              您的邮箱地址
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="请输入您的邮箱地址"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              disabled={isLoading}
            />
            <p className="text-sm text-gray-500 mt-2">
              我们将向此邮箱发送 Augment 账号信息
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                创建订单中...
              </>
            ) : (
              `立即购买 ${priceDisplay}`
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            支持微信支付 • 安全可靠 • 即时交付
          </p>
        </div>
      </div>

      {/* 支付模态框 */}
      {showPayment && (
        <PaymentModal
          orderId={orderId}
          qrCodeUrl={qrCodeUrl}
          amount={price}
          onSuccess={handlePaymentSuccess}
          onClose={handlePaymentClose}
        />
      )}
    </>
  );
}
