{"name": "aug-pay", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.13.0", "@supabase/supabase-js": "^2.53.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "crypto-js": "^4.2.0", "next": "15.4.5", "prisma": "^6.13.0", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}